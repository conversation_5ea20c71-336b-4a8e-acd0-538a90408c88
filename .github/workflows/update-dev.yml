# This is a basic workflow to help you get started with Actions

name: update-dev

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the "main" branch
  push:
    branches: [ "dev" ]
  pull_request:
    branches: [ "dev" ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    steps:
    - name: refresh project
      uses: appleboy/ssh-action@master
      with:
        host: "*************"
        username: "root"
        key: ${{ secrets.ENV_SSH_TEST }}
        port: "22"
        script: |
          cd Co-Wisdom-BE/co_wisdom_server
          bash qzcoach.sh all

    - name: Send notification to <PERSON><PERSON><PERSON>
      uses: wangsijie/feishu-bot@v1
      with:
        uuid: 06912531-f42d-43ea-945c-59d1d9675a74
        text: "dev server deployed"
