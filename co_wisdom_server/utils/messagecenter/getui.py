import time
import json
import redis
import requests
import hashlib
import logging
import random
import string
from django.conf import settings
from celery import shared_task
from celery.exceptions import SoftTimeLimitExceeded

from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.feishu_robot import push_celery_hanging_message
from utils.message.lark_message import LarkMessageCenter
from utils import work_wechat
from utils import work_wechat_content
from wisdom_v2 import utils
from wisdom_v2.models import CoachTask

_logging = logging.getLogger('getui')

getui_token_redis = redis.Redis.from_url(settings.GETUI_TOKEN_URL)


def get_getui_token():
    url = 'https://restapi.getui.com/v2/%s/auth' % settings.APPID
    headers = {"Content-Type": "application/json;charset=UTF-8"}
    app_key = settings.APPKEY
    master_secret = settings.MASTERSECRET

    if not getui_token_redis.get('token'):
        timestamp = int(time.time() * 1000)
        salt = app_key + str(timestamp) + master_secret
        data_sha = hashlib.sha256(salt.encode('utf-8')).hexdigest()
        data = {'sign': data_sha, 'timestamp': timestamp, 'appkey': app_key}
        res = requests.post(url=url, headers=headers, data=json.dumps(data, ensure_ascii=False))

        data = json.loads(res.content.decode("utf-8"))
        if data.get('code') != 0:
            _logging.error('Get Getui Token Error %s' % data)
            return data

        getui_token_redis.set('token', data['data']['token'], ex=86400)
        return data['data']['token']
    else:
        return getui_token_redis.get('token').decode('utf8')


@shared_task(queue='task', ignore_result=True, soft_time_limit=15)
def push_to_single(cid, title, body, url_path='home'):
    try:
        if not all([cid, title, body]):
            return
        token = get_getui_token()
        url = 'https://restapi.getui.com/v2/%s/push/single/cid' % settings.APPID
        headers = {"Content-Type": "application/json;charset=utf-8", "token": token}
        request_id = ''.join(random.sample(string.ascii_letters + string.digits, 15))
        transmission_body = json.dumps({"title": title, "content": body})
        data = {
            "request_id": request_id,
            "settings": {
                "ttl": 3600000,
                "strategy": {
                    "default": 1
                }
            },
            "audience": {
                "cid": [
                    cid
                ]
            },
            # 设置透传消息
            "push_message": {
                "transmission": transmission_body,
            }
            ,
            "push_channel": {
                "ios": {
                    "type": "notify",
                    "aps": {
                        "alert": {
                            "title": title,
                            "body": body
                        },
                        "content-available": 0,
                        "category": "ACTIONABLE"
                    },
                    "auto_badge": "+1",
                    "multimedia": [{
                        "url": url_path,
                        "type": 1,
                        "only_wifi": False
                    }]
                },
                "android": {
                    "ups": {
                        "notification": {
                            "title": title,
                            "body": body,
                            "click_type": "intent",
                            "intent": "intent://io.dcloud.unipush/?#Intent;scheme=unipush;launchFlags=0x4000000;component=com.qzcoach.android/io.dcloud.PandoraEntry;S.UP-OL-SU=true;S.title=%s;S.content=%s;S.payload=test;end" % (title, body)
                        }
                    }
                }
            }
        }
        _logging.info('Push Single Params url:{0}, headers:{1}, data:{2}'.format(url, headers,
                                                                                 json.dumps(data, ensure_ascii=False)))
        res = requests.post(url=url, headers=headers, data=json.dumps(data, ensure_ascii=True))
        # print('res', res)
        response = json.loads(res.content.decode())
        _logging.info('Push Single Response :{0}'.format(response))
        # print(response)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('push_to_single')


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_coach_notice(user, content_type, project_id=None, sender_id=None, receiver_id=None, **kwargs):
    try:
        content, miniapp_content = work_wechat_content.get_work_wechat_content(content_type, **kwargs)

        # 如果没有传递receiver_id，尝试从kwargs中获取user_id
        if receiver_id is None:
            receiver_id = kwargs.get('user_id') or kwargs.get('coach_id')

        content_msg, app_msg, content_is_success, app_is_success = None, None, True, True
        # 需求要文本请求和小程序请求分两次请求
        if content:
            content_is_success, content_msg = work_wechat.WorkWechat().send_message(
                user, 'text', content, project_id=project_id, sender_id=sender_id,
                receiver_id=receiver_id, template_name=content_type)
        if miniapp_content:
            app_is_success, app_msg = work_wechat.WorkWechat().send_message(
                user, 'miniprogram_notice', miniapp_content, project_id=project_id,
                sender_id=sender_id, receiver_id=receiver_id, template_name=content_type)

        # 请求数据同步到sls
        kwargs['content'] = [content, miniapp_content, f'to_user: {user}']  # 企业微信消息有批量发送的情况，需要记录接受者企业微信user信息
        kwargs['user_type'] = 'coach'
        kwargs['user_id'] = kwargs.get('user_id') if kwargs.get('user_id') else kwargs.get('coach_id')
        kwargs['user_name'] = kwargs.get('user_name') if kwargs.get('user_name') else kwargs.get('coach_name')
        kwargs['content_msg'] = content_msg
        kwargs['app_msg'] = app_msg
        kwargs['event_id'] = 'Send_Coach_WechatMessage'
        kwargs['message'] = work_wechat_content.content_type_to_message.get(content_type)
        AliyunSlsLogLayout().send_work_wechat_log_data(**kwargs)
        return content_is_success, app_is_success
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_coach_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_coach_notice:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_coachee_notice(
        sender, text, external_user_id, page,
        title, content_type='add_msg_template', file_name='basicprofile.jpg',
        project_id=None, sender_id=None, receiver_id=None, **kwargs):
    try:
        msg, media_id = work_wechat.WorkWechat().upload_media_image(file_name)
        if not msg:
            return f'{file_name}未获取到media_id'

        # 如果没有传递receiver_id，尝试从kwargs中获取coachee_id
        if receiver_id is None:
            receiver_id = kwargs.get('coachee_id')

        text_content, miniapp_content = work_wechat_content.get_work_wechat_content(
            content_type, sender=sender, text=text,
            external_userid_list=external_user_id, page=page,
            title=title, media_id=media_id)
        state, msg = work_wechat.WorkWechat().add_msg_template(
            external_user_id, miniapp_content, file_name, project_id=project_id,
            sender_id=sender_id, receiver_id=receiver_id, template_name=content_type)

        # 请求数据同步到sls
        kwargs['content'] = miniapp_content
        if not kwargs.get('message'):
            kwargs['message'] = title
        kwargs['user_type'] = 'coachee'
        kwargs['user_id'] = kwargs.get('coachee_id')
        kwargs['event_id'] = kwargs.get('Send_WechatMessage')
        kwargs['user_name'] = kwargs.get('coachee_name')
        AliyunSlsLogLayout().send_work_wechat_log_data(**kwargs)
        return state, msg

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_coach_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_coach_notice:{str(e)}'})


@shared_task(queue='task', ignore_result=True)
def send_user_notice(project_interested_objs, obj):
    try:
        if isinstance(obj, CoachTask):
            utils.send_stakeholder_coach_task_sms_notice(project_interested_objs, obj)
            utils.send_stakeholder_coach_task_email_notice(project_interested_objs, obj)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_user_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_user_notice:{str(e)}'})

# get_getui_token()
# push_to_single('40338dde6f24e730168c754d942f3787', '今日有6位教练可以预约', '预约一次教练辅导来体验如何通过对话解决工作难题')
