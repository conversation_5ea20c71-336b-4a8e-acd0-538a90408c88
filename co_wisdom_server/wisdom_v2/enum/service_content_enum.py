from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 1对1辅导模块教练形式
@unique
class CoachTypeEnum(_ChoiceClass):
    online = 1
    offline = 2

    __describe__ = OrderedDict((
        (online, '线上一对一'),
        (offline, '线下一对一'),
    ))

    @classmethod
    def default(cls):
        return cls.online

    def describe(self):
        return self.__describe__[self.value]


@unique
class OneToOneMatchTypeEnum(_ChoiceClass):
    appoint_coach = 1
    all_project_coach = 2

    __describe__ = OrderedDict((
        (appoint_coach, '指定教练'),
        (all_project_coach, '教练池'),
    ))

    @classmethod
    def default(cls):
        return cls.appoint_coach

    def describe(self):
        return self.__describe__[self.value]


# 集体辅导模块能力标签
@unique
class PowerTagEnum(_ChoiceClass):
    group_leadership = 1
    agile_change = 2
    effective_authorization = 3
    restraining = 4
    synergistic_consolidation = 5
    forge_ahead = 6
    conflict_management = 7
    development_others = 8
    compressive_recovery = 9
    strategic_vision = 10
    incentive_can_assign = 11
    decisive_decisions = 12

    __describe__ = OrderedDict((
        (group_leadership, '集体领导'),
        (agile_change, '敏捷创变'),
        (effective_authorization, '有效授权'),
        (restraining, '知人善任'),
        (synergistic_consolidation, '协同整合'),
        (forge_ahead, '锐意进取'),
        (conflict_management, '冲突管理'),
        (development_others, '发展他人'),
        (compressive_recovery, '抗压复原'),
        (strategic_vision, '战略远见'),
        (incentive_can_assign, '激励赋能'),
        (decisive_decisions, '果断决策'),
    ))

    @classmethod
    def default(cls):
        return cls.group_leadership

    def describe(self):
        return self.__describe__[self.value]


# 数据查看权限数据类型
@unique
class DataTypeEnum(_ChoiceClass):
    evaluation = 1
    article = 2

    __describe__ = OrderedDict((
        (evaluation, '测评'),
        (article, '文章'),
    ))

    @classmethod
    def default(cls):
        return cls.evaluation

    def describe(self):
        return self.__describe__[self.value]


# 教练任务模块教练任务类型
@unique
class CoachTaskTypeEnum(_ChoiceClass):
    ildp = 1
    stakeholder = 2
    tripartite = 3
    phase_review = 4
    final_report = 5

    __describe__ = OrderedDict((
        (ildp, 'ILDP教练目标'),
        (stakeholder, '利益相关者访谈报告'),
        (tripartite, '三方约谈记录'),
        (phase_review, '阶段回顾报告'),
        (final_report, '总结报告'),
    ))

    @classmethod
    def default(cls):
        return cls.ildp

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录模板教练形式
@unique
class InterviewRecordTemplateTypeEnum(_ChoiceClass):
    group_counseling = 1
    one_to_one = 2
    coach_task = 3
    chemical_interview = 4
    stake_holder_interview = 5
    project_one_to_one = 6

    __describe__ = OrderedDict((
        (group_counseling, '线下集体辅导'),
        (one_to_one, '见习用一对一'),
        (coach_task, '教练任务'),
        (chemical_interview, '化学面谈'),
        (stake_holder_interview, '利益相关者访谈'),
        (project_one_to_one, '项目一对一'),
    ))

    @classmethod
    def default(cls):
        return cls.group_counseling

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录模板填写记录的角色
@unique
class InterviewRecordTemplateRoleEnum(_ChoiceClass):
    coach = 1
    student = 2
    coach_student = 3
    stakeholder = 4

    __describe__ = OrderedDict((
        (coach, '教练'),
        (student, '学员'),
        (coach_student, '教练、学员'),
        (stakeholder, '利益相关者'),
    ))

    @classmethod
    def default(cls):
        return cls.coach

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录模板问题类型
@unique
class InterviewRecordTemplateQuestionTypeEnum(_ChoiceClass):
    blank = 1
    single = 2
    multiple = 3
    rating = 4
    power_tag = 5

    __describe__ = OrderedDict((
        (blank, '填空'),
        (single, '单选'),
        (multiple, '多选'),
        (rating, '评分'),
        (power_tag, '能力标签'),
    ))


    @classmethod
    def default(cls):
        return cls.blank

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录模板回答格式类型
@unique
class InterviewRecordTemplateQuestionAnswerTypeEnum(_ChoiceClass):
    normal_text = 1
    action_plan = 2
    habit_formation = 3
    growth_notes = 4
    power_tag = 5
    multiple_text = 6
    company_info = 7
    project_info = 8
    customer_info = 9

    __describe__ = OrderedDict((
        (normal_text, '普通文本框'),
        (action_plan, '行动计划'),
        (habit_formation, '习惯养成'),
        (growth_notes, '成长笔记'),
        (power_tag, '能力标签'),
        (multiple_text, '多条文本'),
        (company_info, '公司信息'),
        (project_info, '项目信息'),
        (customer_info, '客户信息'),
    ))

    @classmethod
    def default(cls):
        return cls.normal_text

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录模板评分格式类型
@unique
class InterviewRecordTemplateQuestionRatingTypeEnum(_ChoiceClass):
    validity = 1
    Immersion = 2
    satisfaction = 3

    __describe__ = OrderedDict((
        (validity, '有效度'),
        (Immersion, '投入度'),
        (satisfaction, '满意度')
    ))

    @classmethod
    def default(cls):
        return cls.validity

    def describe(self):
        return self.__describe__[self.value]


@unique
class ContentOperationBaseChannelEnum(_ChoiceClass):
    android = 1
    ios = 2
    applets = 3

    __describe__ = OrderedDict((
        (android, '安卓'),
        (ios, 'ios'),
        (applets, '小程序'),
    ))

    @classmethod
    def default(cls):
        return cls.android

    def describe(self):
        return self.__describe__[self.value]


@unique
class ContentOperationBaseInputChannelEnum(_ChoiceClass):
    search = 1
    record = 2

    __describe__ = OrderedDict((
        (search, '搜索'),
        (record, '辅导记录'),
    ))

    @classmethod
    def default(cls):
        return cls.search

    def describe(self):
        return self.__describe__[self.value]


@unique
class ContentOperationBaseKeyChannelEnum(_ChoiceClass):
    article = 1
    record = 2

    __describe__ = OrderedDict((
        (article, '文章'),
        (record, '辅导记录'),
    ))

    @classmethod
    def default(cls):
        return cls.article

    def describe(self):
        return self.__describe__[self.value]


@unique
class ArticleTopicEnum(_ChoiceClass):
    counseling = 1
    manage = 2
    collaboration = 3
    style = 4
    authorize_decision = 5
    reading = 6
    release = 7

    __describe__ = OrderedDict((
        (counseling, '辅导下属'),
        (manage, '向上管理'),
        (collaboration, '横向协作'),
        (style, '领导风格'),
        (authorize_decision, '决策授权'),
        (reading, '课前学习'),
        (release, '释放压力'),
    ))

    @classmethod
    def default(cls):
        return cls.counseling

    def describe(self):
        return self.__describe__[self.value]


@unique
class UserBehaviorDataTypeEnum(_ChoiceClass):
    search = 1
    home_error = 2

    __describe__ = OrderedDict((
        (search, '搜索'),
        (home_error, '首页错误数据')
    ))

    @classmethod
    def default(cls):
        return cls.search

    def describe(self):
        return self.__describe__[self.value]


@unique
class WorkWechatUserDepartmentEnum(_ChoiceClass):
    coach = 1
    consultant = 2

    __describe__ = OrderedDict((
        (coach, '教练'),
        (consultant, '项目运营')
    ))

    @classmethod
    def default(cls):
        return cls.coach

    def describe(self):
        return self.__describe__[self.value]


@unique
class EvaluationWriteRoleEnum(_ChoiceClass):
    coachee = 1
    coachee_stakeholder = 2

    __describe__ = OrderedDict((
        (coachee, '被教练者'),
        (coachee_stakeholder, '被教练者-利益相关者')
    ))

    @classmethod
    def default(cls):
        return cls.coachee

    def describe(self):
        return self.__describe__[self.value]


@unique
class RemindTypeEnum(_ChoiceClass):
    sms = 1
    email = 2

    __describe__ = OrderedDict((
        (sms, '短信'),
        (email, '邮件')
    ))

    @classmethod
    def default(cls):
        return cls.sms

    def describe(self):
        return self.__describe__[self.value]

@unique
class MultipleAssociationRelationTypeEnum(_ChoiceClass):
    stakeholder_change_observation = 1
    stakeholder_coach_task = 2

    __describe__ = OrderedDict((
        (stakeholder_change_observation, '利益相关者-改变观察反馈'),
        (stakeholder_coach_task, '利益相关者-利益相关者调研'),
    ))

    @classmethod
    def default(cls):
        return cls.stakeholder_change_observation

    def describe(self):
        return self.__describe__[self.value]

@unique
class PersonalReportTypeEnum(_ChoiceClass):
    change_observation_report = 1
    notes_report = 2
    summary_report = 3

    __describe__ = OrderedDict((
        (change_observation_report, '改变观察反馈报告'),
        (notes_report, '利益相关者访谈纪要'),
        (summary_report, '利益相关者访谈总结报告'),
    ))

    @classmethod
    def default(cls):
        return cls.change_observation_report

    def describe(self):
        return self.__describe__[self.value]


@unique
class NewCoachTaskTypeEnum(_ChoiceClass):
    default_task = 1
    stakeholder_research = 2

    __describe__ = OrderedDict((
        (default_task, '默认无特殊'),
        (stakeholder_research, '利益相关者调研任务'),
    ))

    @classmethod
    def default(cls):
        return cls.default_task

    def describe(self):
        return self.__describe__[self.value]


@unique
class PdfReportTypeEnum(_ChoiceClass):
    lbi_personal_report = 1
    lbi_project_report = 2
    manage_evaluation_report = 3
    change_observation_report = 4
    stakeholder_report = 5
    default_coach_task = 6
    lbi_personal_report_company_manager = 7
    coach_contract = 101
    project_progress_report = 8
    lbi_self_evaluation_report = 9

    __describe__ = OrderedDict((
        (lbi_personal_report, 'lbi个人报告'),
        (lbi_project_report, 'lbi项目报告'),
        (manage_evaluation_report, '教练型管理者报告'),
        (change_observation_report, '改变观察反馈报告'),
        (stakeholder_report, '利益相关者调研任务报告'),
        (default_coach_task, '默认教练任务'),
        (coach_contract, '教练合同-agreement页面使用'),
        (project_progress_report, '企业管理员-项目进度报告'),
        (lbi_self_evaluation_report, 'lbi自评报告'),

    ))

    @classmethod
    def default(cls):
        return cls.lbi_personal_report

    def describe(self):
        return self.__describe__[self.value]


@unique
class CustomerPortraitTypeEnum(_ChoiceClass):
    personal = 1
    group = 2

    __describe__ = OrderedDict((
        (personal, '个人画像'),
        (group, '群体画像'),
    ))

    @classmethod
    def default(cls):
        return cls.personal

    def describe(self):
        return self.__describe__[self.value]


@unique
class PortraitInterviewTypeEnum(_ChoiceClass):
    on_line = 1
    offline = 2
    on_line_and_offline = 3

    __describe__ = OrderedDict((
        (on_line, '线上'),
        (offline, '线下'),
        (on_line_and_offline, '线上+线下'),
    ))

    @classmethod
    def default(cls):
        return cls.on_line

    def describe(self):
        return self.__describe__[self.value]


@unique
class NoticeChannelTypeEnum(_ChoiceClass):

    work_wechat = 1
    sms = 2
    email = 3
    feishu = 4
    mini_program = 5

    __describe__ = OrderedDict((
        (work_wechat, '企业微信'),
        (sms, '短信'),
        (email, '邮件'),
        (feishu, '飞书'),
        (mini_program, '小程序'),
    ))

    @classmethod
    def default(cls):
        return cls.email

    def describe(self):
        return self.__describe__[self.value]


@unique
class NoticeTemplateTypeEnum(_ChoiceClass):

    create_account = 1
    change_observation = 2
    stakeholder = 3
    coach_resume = 4
    company_manage_create_account = 5
    chemical_interview_appointment = 6
    chemical_interview_feedback = 7
    chemical_interview_schedule = 8
    stakeholder_interview_reservation = 9
    coachee_invite_stakeholder_interview_reservation = 10
    stakeholder_interview_fill_in_report = 11
    stakeholder_interview_schedule = 12
    change_observation_customer = 13
    customer_infomation_collection = 14
    create_account_mop = 15
    increase_interview_hour = 16

    __describe__ = OrderedDict((
        (create_account, '被教练者账号开通通知'),
        (change_observation, '改变观察问卷'),
        (stakeholder, '利益相关者调研问卷'),
        (coach_resume, '教练简历发送通知'),
        (company_manage_create_account, '企业管理员账号开通通知'),
        (chemical_interview_appointment, '化学面谈预约提醒'),
        (chemical_interview_feedback, '化学面谈反馈提醒'),
        (chemical_interview_schedule, '化学面谈反馈可预约日程设置提醒'),
        (stakeholder_interview_reservation, '利益相关者访谈预约提醒'),
        (coachee_invite_stakeholder_interview_reservation, '客户邀请利益相关者预约访谈提醒'),
        (stakeholder_interview_fill_in_report, '利益相关者访谈报告填写提醒'),
        (stakeholder_interview_schedule, '利益相关者访谈可预约日程设置提醒'),
        (change_observation_customer, '改变观察反馈客户通知'),
        (customer_infomation_collection, '客户信息收集通知'),
        (create_account_mop, 'MOP被教练者账号开通通知'),
        (increase_interview_hour, '辅导小时数增加通知'),
    ))

    @classmethod
    def default(cls):
        return cls.create_account

    def describe(self):
        return self.__describe__[self.value]

@unique
class ProjectCoachStatusEnum(_ChoiceClass):

    adopt = 1
    wait = 2
    fail = 3

    __describe__ = OrderedDict((
        (adopt, '通过'),
        (wait, '待定'),
        (fail, '不通过'),
    ))

    @classmethod
    def default(cls):
        return cls.adopt

    def describe(self):
        return self.__describe__[self.value]

@unique
class PersonaSourceEnum(_ChoiceClass):
    natural_flow = 1
    coachee_recommend = 2
    coach_recommend = 3
    coach_invitation = 4
    activity = 5
    wechat_official_account = 6
    wechat_official_account_article = 7
    video_live = 8
    sms = 9

    __describe__ = OrderedDict((
        (natural_flow, '自然流量'),
        (coachee_recommend, '客户推荐'),
        (coach_recommend, '教练推荐'),
        (coach_invitation, '教练邀请'),
        (activity, '运营活动'),
        (wechat_official_account, '微信公众号'),
        (wechat_official_account_article, '微信公众号文章'),
        (video_live, '视频号直播'),
        (sms, '短信链接'),
    ))

    @classmethod
    def default(cls):
        return cls.natural_flow

    def describe(self):
        return self.__describe__[self.value]


@unique
class UserInviteTypeEnum(_ChoiceClass):
    coach_resume = 1
    interview = 2
    activity = 3
    coach_share = 4
    wechat_official_account = 5
    wechat_official_account_article = 6
    video_live = 7
    sms = 8

    __describe__ = OrderedDict((
        (coach_resume, '教练简历'),
        (interview, '辅导记录'),
        (activity, '运营活动'),
        (coach_share, '教练分享'),
        (wechat_official_account, '微信公众号'),
        (wechat_official_account_article, '微信公众号文章'),
        (video_live, '视频号直播'),
        (sms, '短信链接'),
    ))

    @classmethod
    def default(cls):
        return cls.coach_resume

    @classmethod
    def source_coach_invitation(cls):
        """辅导收入计算时，影响服务费计算的邀请的来源"""
        return [cls.coach_resume, cls.coach_share]

    def describe(self):
        return self.__describe__[self.value]


@unique
class QuickLinkStatusEnum(_ChoiceClass):
    shelf = 1
    un_shelf = 2

    __describe__ = OrderedDict((
        (shelf, '上架'),
        (un_shelf, '下架'),
    ))

    @classmethod
    def default(cls):
        return cls.shelf

    def describe(self):
        return self.__describe__[self.value]


@unique
class AssociationTypeEnum(_ChoiceClass):
    article = 1
    theme = 2
    external_link = 3
    interview = 4
    not_jump = 5

    __describe__ = OrderedDict((
        (article, '文章'),
        (theme, '主题'),
        (external_link, '外部链接'),
        (interview, '预约辅导'),
        (not_jump, '无跳转'),
    ))

    @classmethod
    def default(cls):
        return cls.article

    def describe(self):
        return self.__describe__[self.value]


@unique
class SuitableObjectEnum(_ChoiceClass):
    to_B_coach = 1
    to_B_student = 2
    to_C_coach = 3
    to_C_student = 4

    __describe__ = OrderedDict((
        (to_B_coach, 'B端教练'),
        (to_B_student, 'B端客户'),
        (to_C_coach, 'C端教练'),
        (to_C_student, 'C端客户'),
    ))

    @classmethod
    def default(cls):
        return cls.to_B_student

    def describe(self):
        return self.__describe__[self.value]


@unique
class CoachAuthEnum(_ChoiceClass):
    ICF_ACC = 1
    ICF_PCC = 2
    ICF_MCC = 3
    PREPARE_ICF_PCC = 4
    PREPARE_ICF_ACC = 5

    __describe__ = OrderedDict((
        (ICF_ACC, 'ICF认证ACC教练'),
        (ICF_PCC, 'ICF认证PCC教练'),
        (ICF_MCC, 'ICF认证MCC教练'),
        (PREPARE_ICF_PCC, '通过PCC口试'),
        (PREPARE_ICF_ACC, '通过ACC口试'),
    ))

    @classmethod
    def default(cls):
        return cls.ICF_ACC

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def get_value_from_description(cls, description):
        for key, value in cls.__describe__.items():
            if str(description).upper() in value.upper():
                return key
        return None  # 如果没有匹配，返回None

    @classmethod
    def get_image(cls):
        return {
            str(cls.ICF_ACC.value): 'https://static.qzcoach.com/app/view_background/resumeMedal/acc_v2.png',
            str(cls.ICF_PCC.value): 'https://static.qzcoach.com/app/view_background/resumeMedal/pcc_v2.png',
            str(cls.ICF_MCC.value): 'https://static.qzcoach.com/app/view_background/resumeMedal/mcc_v2.png',
            str(cls.PREPARE_ICF_PCC.value): 'https://static.qzcoach.com/app/view_background/resumeMedal/senior_coach_medal_v3.png',
            str(cls.PREPARE_ICF_ACC.value): 'https://static.qzcoach.com/app/view_background/resumeMedal/junior_coach_medal_v3.png',
        }

    @classmethod
    def get_resume_tag_name(cls, value):
        data = {
            str(cls.ICF_ACC.value): 'ACC',
            str(cls.ICF_PCC.value): 'PCC',
            str(cls.ICF_MCC.value): 'MCC',
            str(cls.PREPARE_ICF_PCC.value): '通过PCC口试',
            str(cls.PREPARE_ICF_ACC.value): '通过ACC口试',
        }
        return data.get(str(value))


@unique
class CoachWorkingYearsEnum(_ChoiceClass):
    one_years = 1
    three_years = 2
    five_years = 3
    eight_years = 4
    ten_years = 5

    __describe__ = OrderedDict((
        (one_years, '一年以上'),
        (three_years, '三年以上'),
        (five_years, '五年以上'),
        (eight_years, '八年以上'),
        (ten_years, '十年以上'),
    ))

    @classmethod
    def default(cls):
        return cls.one_years

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def get_value_from_description(cls, description):
        for key, value in cls.__describe__.items():
            if description in value:
                return key
        return None  # 如果没有匹配，返回None


@unique
class CoachOfferStatusEnum(_ChoiceClass):
    not_confirm = 1
    joined = 2
    rejected = 3
    expired = 4
    company_rejected = 5


    __describe__ = OrderedDict((
        (not_confirm, '未确认'),
        (joined, '已加入'),
        (rejected, '已拒绝'),
        (expired, '已过期'),
        (company_rejected, '未通过企业面试')

    ))

    @classmethod
    def default(cls):
        return cls.not_confirm

    def describe(self):
        return self.__describe__[self.value]


@unique
class ProjectOfferStatusEnum(_ChoiceClass):
    not_sent = 1
    sent = 2

    __describe__ = OrderedDict((
        (not_sent, '未发送'),
        (sent, '已发送')
    ))

    @classmethod
    def default(cls):
        return cls.not_sent

    def describe(self):
        return self.__describe__[self.value]


@unique
class CoachSourceTypeEnum(_ChoiceClass):
    company = 1
    offer = 2

    __describe__ = OrderedDict((
        (company, '企业添加'),
        (offer, 'offer接入')
    ))

    @classmethod
    def default(cls):
        return cls.company

    def describe(self):
        return self.__describe__[self.value]


@unique
class ScheduleTypeEnum(_ChoiceClass):
    unavailable = 1
    interview = 2
    other = 3
    available = 4
    company_interview = 5

    __describe__ = OrderedDict((
        (unavailable, '忙碌'),
        (interview, '辅导'),
        (other, '忙碌（线上平台）'),  # 已弃用
        (available, '可预约'),
        (company_interview, '企业面试'),
    ))

    @classmethod
    def default(cls):
        return cls.unavailable

    def describe(self):
        return self.__describe__[self.value]


@unique
class ScheduleApplyTypeEnum(_ChoiceClass):
    personal = 1
    project = 2
    activity = 3

    __describe__ = OrderedDict((
        (personal, '个人辅导'),
        (project, '企业项目'),
        (activity, '教练活动'),
    ))

    @classmethod
    def default(cls):
        return cls.personal

    def describe(self):
        return self.__describe__[self.value]


@unique
class GrowthGoalsModelTypeEnum(_ChoiceClass):
    limit = 1
    fixed = 2

    __describe__ = OrderedDict((
        (fixed, '固定时间'),
        (limit, '限制时长'),
    ))

    @classmethod
    def default(cls):
        return cls.fixed

    def describe(self):
        return self.__describe__[self.value]

@unique
class ActivityTypeEnum(_ChoiceClass):
    """活动类型"""
    public_welfare_coach = 1

    __describe__ = OrderedDict((
        (public_welfare_coach, '公益教练'),
    ))

    @classmethod
    def default(cls):
        return cls.public_welfare_coach

    def describe(self):
        return self.__describe__[self.value]


class PersonalActivityTypeEnum(_ChoiceClass):
    """个人活动类型"""
    coach_share = 1
    wechat_share = 2

    __describe__ = OrderedDict((
        (coach_share, '教练分享'),
        (wechat_share, '微信分享'),
    ))

    @classmethod
    def default(cls):
        return cls.coach_share

    def describe(self):
        return self.__describe__[self.value]


@unique
class ActivityCoachStatusEnum(_ChoiceClass):
    not_invitation = 1
    joined = 2
    rejected = 3
    not_response = 4
    expired = 5

    __describe__ = OrderedDict((
        (not_invitation, '免邀请'),
        (joined, '已加入'),
        (rejected, '已拒绝'),
        (not_response, '未回复'),
        (expired, '已过期'),

    ))

    @classmethod
    def default(cls):
        return cls.not_invitation

    def describe(self):
        return self.__describe__[self.value]

@unique
class UserQueryRecordTypeEnum(_ChoiceClass):
    theme = 1
    quick_link = 2
    resume = 3

    __describe__ = OrderedDict((
        (theme, '主题'),
        (quick_link, '金刚区'),
        (resume, '简历'),

    ))

    @classmethod
    def default(cls):
        return cls.theme

    def describe(self):
        return self.__describe__[self.value]

@unique
class ChangeObservationInviteTypeEnum(_ChoiceClass):
    stakeholders = 1
    customer = 2

    __describe__ = OrderedDict((
        (stakeholders, '利益相关者'),
        (customer, '客户'),

    ))

    @classmethod
    def default(cls):
        return cls.stakeholders

    def describe(self):
        return self.__describe__[self.value]


@unique
class AppSelectArticleTypeEnum(_ChoiceClass):
    platform_articles = 1
    wechat_official_account = 2

    __describe__ = OrderedDict((
        (platform_articles, '平台内文章'),
        (wechat_official_account, '微信公众号文章'),

    ))

    @classmethod
    def default(cls):
        return cls.platform_articles

    def describe(self):
        return self.__describe__[self.value]


class EvaluationReportTypeEnum(_ChoiceClass):
    lbi_self_evaluation_report = 1
    lbi_personal_report = 2
    manage_evaluation_report = 3

    __describe__ = OrderedDict((
        (lbi_self_evaluation_report, 'lbi自评报告'),
        (lbi_personal_report, 'lbi报告'),
        (manage_evaluation_report, '教练型管理者报告'),
    ))

    @classmethod
    def default(cls):
        return cls.lbi_personal_report

    def describe(self):
        return self.__describe__[self.value]


class TagObjectTypeEnum(_ChoiceClass):
    project = 1
    coach = 2
    company = 3

    __describe__ = OrderedDict((
        (project, '项目'),
        (coach, '教练'),
        (company, '企业'),

    ))

    @classmethod
    def default(cls):
        return cls.project

    def describe(self):
        return self.__describe__[self.value]


class TagSubTypeEnum(_ChoiceClass):
    multiple = 1
    single = 2

    __describe__ = OrderedDict((
        (multiple, '多选'),
        (single, '单选'),
    ))

    @classmethod
    def default(cls):
        return cls.multiple

    def describe(self):
        return self.__describe__[self.value]


class TagRequirementConfigTypeEnum(_ChoiceClass):
    project_requirements = 1

    __describe__ = OrderedDict((
        (project_requirements, '项目需求标签'),
    ))

    @classmethod
    def default(cls):
        return cls.project_requirements

    def describe(self):
        return self.__describe__[self.value]
