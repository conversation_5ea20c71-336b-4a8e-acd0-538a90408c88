from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils import task
from utils.api_response import parameter_error_response, success_response
from wisdom_v2.common import interview_public
from wisdom_v2.views.change_observation_action import AdminChangeObservationSerializer
from wisdom_v2.models import ChangeObservation
from wisdom_v2.enum.service_content_enum import ChangeObservationInviteTypeEnum


class AdminChangeObservationViewSet(viewsets.ModelViewSet):
    queryset = ChangeObservation.objects.filter(deleted=False)
    serializer_class = AdminChangeObservationSerializer

    @swagger_auto_schema(
        operation_id='改变观察反馈发送提醒',
        operation_summary='改变观察反馈发送提醒',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='改变观察id'),
            }
        ),
        tags=['后台改变观察反馈相关']
    )
    @action(methods=['post'], detail=False, url_path='send_message')
    def change_observation_send_message(self, request, *args, **kwargs):
        try:
            change_observation_id = request.data.get('id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
        except:
            return parameter_error_response('未获取到改变观察信息')
        # if change_observation.invite_type == ChangeObservationInviteTypeEnum.customer.value:
        #     return parameter_error_response('客户邀请不支持点击发送')
        if change_observation.write_condition != 0:
            interview_time = interview_public.get_user_interview_time(change_observation.project_member)
            if interview_time < change_observation.write_condition:
                return parameter_error_response('发送失败，当前不满足提醒条件')
        task.send_change_observation_stakeholder_notice.delay(change_observation, is_click_to_send=True)
        return success_response()
