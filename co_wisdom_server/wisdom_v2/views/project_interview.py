import redis

from django.conf import settings
from django.db import transaction
from rest_framework.viewsets import ModelViewSet
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q
from datetime import datetime, timedelta

from utils import task
from wisdom_v2.app_views.app_interview_actions import InterviewRecordQuestionAnswerEditDetailSerializer, \
    InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer
from wisdom_v2.common import interview_public, project_coach_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum
from wisdom_v2.models import ProjectInterview, ProjectMember, ProjectCoach, ProjectInterested, Coach, WorkWechatUser
from wisdom_v2.models_file import StakeholderInterview
from wisdom_v2.views.project_interview_action import ProjectInterviewListSerializer, \
    BackendProjectInterviewCreateSerializer, ProjectInterviewDetailListSerializer, ModifyProjectInterviewSerializer
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum, \
    GroupCoachTypeEnum, InterviewRecordTypeEnum, ProjectInterviewPlaceTypeEnum
from wisdom_v2.app_views.app_interview_record_actions import ProjectInterviewRecordSerializer
from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


class BackendProjectInterviewViewSet(ModelViewSet):
    queryset = ProjectInterview.objects.filter(deleted=False).all().order_by('-created_at')
    serializer_class = ProjectInterviewListSerializer

    @swagger_auto_schema(
        operation_id='辅导管理列表',
        operation_summary='辅导管理列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id（必传）', type=openapi.TYPE_NUMBER),
            openapi.Parameter('type', openapi.IN_QUERY, description='辅导类型 1-1对1辅导 2-集体辅导 3-化学面谈', type=openapi.TYPE_NUMBER),
            openapi.Parameter('member_type', openapi.IN_QUERY, description='用户类型 1-被教练者 2-利益相关者（必传）',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('status', openapi.IN_QUERY, description='状态 1未完成 2已完成 3异常（必传）',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_name', openapi.IN_QUERY, description='被教练者姓名',
                              type=openapi.TYPE_STRING)
        ],
        tags=['辅导管理相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = int(request.query_params.get('project_id', None))
            member_type = int(request.query_params.get('member_type', None))
            status = int(request.query_params.get('status', None))
            project_member_name = request.query_params.get('project_member_name', None)
            type = int(request.query_params.get('type', 0))
        except (KeyError, ValueError):
            return parameter_error_response('传入参数错误')
        queryset = self.get_queryset().filter(public_attr__project_id=project_id)
        if type:
            if type == 1:
                queryset = queryset.filter(type=type, place_category=1)
            elif type == 2:
                queryset = queryset.filter(
                    place_category=3,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring).distinct()
            elif type == 3:
                queryset = queryset.filter(type=type, place_category=1)
            elif type == 4:
                queryset = queryset.filter(
                    place_category=3,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring).distinct()
        if project_member_name:
            queryset = queryset.filter(Q(public_attr__target_user__name__icontains=project_member_name) |
                                     Q(public_attr__target_user__true_name__icontains=project_member_name) |
                                     Q(public_attr__target_user__phone__icontains=project_member_name))
        if member_type == 1 or member_type == '1':
            queryset = queryset.exclude(type=4)
        elif member_type == 2 or member_type == '2':
            queryset = queryset.filter(type=4)
        else:
            return parameter_error_response('用户类型参数错误')
        now = datetime.now()
        if status == 1 or status == '1':   # 未完成： 未开始、进行中

            queryset = queryset.filter(Q(public_attr__start_time__gt=now) |
                                       Q(public_attr__start_time__lt=now, public_attr__end_time__gt=now)).\
                exclude(public_attr__status=6)

        elif status == 2 or status == '2':  # 已完成： 已完成
            queryset = queryset.filter(public_attr__end_time__lt=now).exclude(public_attr__status=6)
        elif status == 3 or status == '3':  # 取消
            queryset = queryset.filter(public_attr__status=6)
        else:
            return parameter_error_response('辅导状态参数错误')

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset.order_by('-public_attr__start_time'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='辅导管理预约辅导教练/利益相关者列表',
        operation_summary='辅导管理预约辅导教练/利益相关者列表',
        manual_parameters=[
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='被教练者id（必传）',
                              type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_stakeholder_list')
    def coach_list(self, request, *args, **kwargs):
        try:
            project_member_id = int(request.query_params.get('project_member_id', None))
            project_member = ProjectMember.objects.get(pk=project_member_id)
        except:
            return parameter_error_response('传入项目成员信息错误')
        coach_result = []
        stakeholder_result = []

        project_coaches = project_coach_public.get_project_user_coach(project_member)
        if project_coaches and project_coaches.exists():
            for project_coach in project_coaches:
                coach_result.append({"coach_id": project_coach.coach_id,
                                     "coach_user_id": project_coach.coach.user.pk,
                                     "coach_name": project_coach.coach.user.cover_name})
            project_interested = ProjectInterested.objects.filter(master=project_member.user,
                                                                  project=project_member.project, deleted=False)
            if project_interested.exists():
                for p in project_interested:
                    stakeholder_result.append({"stakeholder_id": p.interested_id,
                                               "stakeholder_name": p.interested.cover_name})
        result = {"coach": coach_result, "stakeholder": stakeholder_result}

        return success_response(result)

    @swagger_auto_schema(
        operation_id='辅导管理-预约辅导',
        operation_summary='辅导管理-预约辅导',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者id(必传)'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id (必传)'),
                'interview_date': openapi.Schema(type=openapi.TYPE_STRING, description='辅导日期 YYYY-MM-DD'),
                'times': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导时长'),
                'start_interview_time': openapi.Schema(type=openapi.TYPE_STRING, description='辅导开始时间 HH:MM'),
                'end_interview_time': openapi.Schema(type=openapi.TYPE_STRING, description='辅导结束时间 HH:MM'),
                'stakeholder_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者id'),
                'interview_obj': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导对象 1-被教练者 2-利益相关者'),
                'type': openapi.Schema(type=openapi.TYPE_STRING, description='教练类型  1-线上一对一辅导 3-化学面谈 4-利益相关者访谈'),
                'place_type': openapi.Schema(type=openapi.TYPE_STRING, description='辅导地址类型 1-线上 2-线下'),
                'place': openapi.Schema(type=openapi.TYPE_STRING, description='具体地址'),
                'interview_subject': openapi.Schema(type=openapi.TYPE_STRING, description='教练形式 2-多方约谈 6-影子观察'),

            }
        ),
        tags=['辅导管理相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = BackendProjectInterviewCreateSerializer
        return success_response(super().create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='辅导记录详情',
        operation_summary='辅导记录详情',
        manual_parameters=[
            openapi.Parameter('project_interview_id', openapi.IN_QUERY, description='辅导id（必传）',
                              type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='interview_record_detail')
    def interview_record_detail(self, request, *args, **kwargs):
        try:
            project_interview_id = request.query_params.get('project_interview_id')
            project_interview = ProjectInterview.objects.get(pk=project_interview_id)
        except:
            return parameter_error_response('当前辅导不存在')

        if project_interview.type == ProjectInterviewTypeEnum.formal_interview:
            if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
                serializer = InterviewRecordQuestionAnswerEditDetailSerializer(project_interview, context={'role': 0})
                data = serializer.data
                return success_response(data)
            elif project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:

                if project_interview.record_type == InterviewRecordTypeEnum.question_and_answer.value:
                    serializer = ProjectInterviewRecordSerializer(project_interview)
                elif project_interview.record_type == InterviewRecordTypeEnum.questionnaire.value:
                    serializer = InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(
                        project_interview, context={'role': 0})
                else:
                    return parameter_error_response('未知的辅导类型')
                data = serializer.data
                return success_response(data, request=request)
            else:
                return parameter_error_response('未知的辅导类型')
        elif project_interview.type in [ProjectInterviewTypeEnum.chemical_interview, ProjectInterviewTypeEnum.stakeholder_interview]:
            if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:
                serializer = InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(project_interview, context={'role': 0})
                data = serializer.data
                return success_response(data)
            else:
                return parameter_error_response('未知的辅导类型')
        else:
            return parameter_error_response('未知的辅导类型')

    @swagger_auto_schema(
        operation_id='辅导明细详情',
        operation_summary='辅导明细详情',
        manual_parameters=[
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coachee_name', openapi.IN_QUERY, description='客户名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
            openapi.Parameter('order_state', openapi.IN_QUERY, description='订单状态 1-待付款 2-已关闭 3-已完成 4-已退款', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('interview_status', openapi.IN_QUERY, description='辅导状态', type=openapi.TYPE_NUMBER),
            openapi.Parameter('is_settlement', openapi.IN_QUERY, description='结算状态', type=openapi.TYPE_BOOLEAN),
            openapi.Parameter('interview_record_status', openapi.IN_QUERY, description='填写状态', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='detail_list')
    def get_interview_detail_list(self, request, *args, **kwargs):
        state, queryset = interview_public.get_project_interview_details(request.query_params)
        if not state:
            return parameter_error_response(queryset)
        self.serializer_class = ProjectInterviewDetailListSerializer
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset.order_by('-public_attr__start_time'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改辅导明细',
        operation_summary='修改辅导明细',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'is_settlement': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='结算状态'),
                'interview_id': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='辅导记录id'),
            }
        ),
        tags=['辅导管理相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def post_interview_detail(self, request, *args, **kwargs):

        try:
            is_settlement = request.data.get('is_settlement')
            interview_ids = request.data.get('interview_ids')
            if not isinstance(interview_ids, list):
                return parameter_error_response('辅导记录参数类型错误')
            if not isinstance(is_settlement, bool):
                return parameter_error_response('结算状态参数类型错误')
            ProjectInterview.objects.filter(
                id__in=interview_ids, deleted=False).update(is_settlement=is_settlement)
            return success_response()
        except Exception as e:
            return parameter_error_response(str(e))


    @swagger_auto_schema(
        operation_id='辅导详情',
        operation_summary='辅导详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='辅导id', type=openapi.TYPE_STRING),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_interview_detail(self, request, *args, **kwargs):

        try:
            interview_id = request.query_params.get('interview_id')
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导记录不存在')
        except Exception as e:
            return parameter_error_response('参数错误')
        self.serializer_class = ModifyProjectInterviewSerializer
        serializer = self.get_serializer(interview)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改辅导',
        operation_summary='修改辅导',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='辅导记录id'),
            }
        ),
        tags=['辅导管理相关']
    )
    @action(methods=['post'], detail=False, url_path='modify')
    def modify_interview_detail(self, request, *args, **kwargs):

        try:
            interview_id = request.data.get('interview_id')
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)

            interview_date = request.data.get('interview_date')
            start_interview_time = request.data.get('start_interview_time')
            end_interview_time = request.data.get('end_interview_time')
            start_time = f"{interview_date} {start_interview_time}"
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导记录不存在')
        except Exception as e:
            return parameter_error_response('参数错误')

        with transaction.atomic():
            for field in ['type', 'place_type', 'place', 'interview_subject']:
                value = request.data.get(field)
                if value is not None:
                    setattr(interview, field, value)
            interview.save()

            if start_interview_time:
                if end_interview_time == '24:00':
                    tmp_end_time = datetime.strptime(interview_date, '%Y-%m-%d')
                    tmp_end_time = tmp_end_time + timedelta(days=1)
                    end_time = tmp_end_time.strftime('%Y-%m-%d %H:%M')
                else:
                    end_time = f"{interview_date} {end_interview_time}"

            # 管理后台没有版本参数，user_update_interview_time管理后台和小程序公用
            # mp={'version': None}在compare_version检测中，会认为是最新版本。
            interview_public.user_update_interview_time(interview, start_time, end_time)
            # 更新会议信息
            task.update_interview_meeting.delay(interview.id)

        return success_response()

    @swagger_auto_schema(
        operation_id='取消辅导',
        operation_summary='取消辅导',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='辅导记录id'),
            }
        ),
        tags=['辅导管理相关']
    )
    @action(methods=['post'], detail=False, url_path='cancellation')
    def cancellation_project_interview(self, request, *args, **kwargs):

        try:
            interview_id = request.data.get('interview_id')
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导记录不存在')
        except Exception as e:
            return parameter_error_response(str(e))

        if interview.coach_record_status or interview.coachee_record_status:
            return parameter_error_response('该辅导已经填写辅导记录，不能取消')

        interview_public.cancel_interview(interview, '后台取消')
        params = {
            'url': request.get_full_path(),
            'token': request.META.get('HTTP_AUTHORIZATION'),
            'user_id': request.user.pk,
            'true_name': request.user.cover_name,
            'body': request.body.decode('utf8') if request.body.decode('utf8') else {},
            'params': request.GET if request.GET else {},
            'message': '取消辅导'
        }
        task.send_business_api_sls_log.delay(params)
        return success_response()

    @swagger_auto_schema(
        operation_id='可预约化学面谈项目成员列表',
        operation_summary='可预约化学面谈项目成员列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='chemical_stakeholder_interview_coach_list')
    def chemical_stakeholder_interview_coach_list(self, request, *args, **kwargs):
        try:
            type = int(request.query_params['type'])
            project_member_id = request.query_params['project_member_id']
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response()
        data = {}
        if type == ProjectInterviewTypeEnum.chemical_interview:  # 化学面谈
            chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
            if not chemical_interview_module:
                return parameter_error_response('当前用户未配置化学面谈')
            if datetime.now().date() < chemical_interview_module.start_time:
                return parameter_error_response('当前用户化学面谈未开始')
            if datetime.now().date() > chemical_interview_module.end_time:
                return parameter_error_response('当前用户化学面谈已结束')
            if chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected).exists():
                return parameter_error_response('当前用户化学面谈已选定教练')
            if chemical_interview_module.coaches.filter(
                    deleted=False, interview__isnull=False,
                    chemical_interview_status=ChemicalInterviewStatusEnum.unselected
            ).count() == chemical_interview_module.max_interview_number:
                return parameter_error_response('当前客户可预约次数用尽')
            chemical_interview = chemical_interview_module.coaches.filter(
                deleted=False, interview__isnull=True,
                chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)
            if not chemical_interview.exists():
                return parameter_error_response('当前客户没有可预约的化学面谈')

            # 如果是自主选择，没有绑定ChemicalInterview2Coach，直接从ProjectCoach读取
            if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.auto_select.value:
                chemical_interview = ProjectCoach.objects.filter(
                    project=project_member.project_id, resume__isnull=False, member__isnull=True,
                    project_group_coach__isnull=True, deleted=False)
            coach_result = [
                {"coach_id": c.coach.id,
                 "coach_name": c.coach.user.cover_name,
                 "coach_user_id": c.coach.user_id} for c in chemical_interview]
            data = {
                "coach": coach_result,
                "start_date": chemical_interview_module.start_time.strftime('%Y-%m-%d'),
                "end_date": chemical_interview_module.end_time.strftime('%Y-%m-%d'),
                "duration": chemical_interview_module.duration
            }
        elif type == ProjectInterviewTypeEnum.stakeholder_interview:  # 利益相关者访谈
            stakeholder_interview_module = project_member.stakeholder_interview_module.filter(deleted=False).first()
            if not stakeholder_interview_module:
                return parameter_error_response('当前用户未配置利益相关者访谈')
            if datetime.now().date() < stakeholder_interview_module.start_date:
                return parameter_error_response('当前用户利益相关者访谈未开始')
            if datetime.now().date() > stakeholder_interview_module.end_date:
                return parameter_error_response('当前用户利益相关者访谈已结束')
            if not stakeholder_interview_module.stakeholder_interview.filter(
                    deleted=False, interview__isnull=True).exists():
                return parameter_error_response('当前客户没有可预约的利益相关者访谈')
            coach = Coach.objects.filter(id=project_member.coach_id, deleted=False).first()
            if not coach:
                return parameter_error_response('当前客户没有匹配教练')
            coach_result = [
                {"coach_id": coach.id, "coach_name": coach.user.cover_name, "coach_user_id": coach.user_id}
            ]
            data = {
                "coach": coach_result,
                "start_date": stakeholder_interview_module.start_date.strftime('%Y-%m-%d'),
                "end_date": stakeholder_interview_module.end_date.strftime('%Y-%m-%d'),
                "duration": stakeholder_interview_module.duration
            }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='后台预约利益相关者访谈获取利益相关者列表',
        operation_summary='后台预约利益相关者访谈获取利益相关者列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导管理相关']
    )
    @action(methods=['get'], detail=False, url_path='stakeholder_interview_list')
    def stakeholder_interview_list(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params['project_member_id']
            coach_user_id = request.query_params['coach_user_id']
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
            coach = Coach.objects.get(deleted=False, user_id=coach_user_id)
        except (KeyError, ProjectMember.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response()
        stakeholder_interview = StakeholderInterview.objects.filter(
            deleted=False, interview__isnull=True, stakeholder_interview_module__project_member=project_member,
            stakeholder_interview_module__deleted=False,
            stakeholder_interview_module__start_date__lte=datetime.now().date(),
            stakeholder_interview_module__end_date__gte=datetime.now().date()
        )
        if stakeholder_interview.exists():
            results = [{"stakeholder_id": s.project_interested.interested_id,
                        "stakeholder_name": s.project_interested.interested.cover_name} for s in stakeholder_interview]
        else:
            results = []
        return success_response(results)
    
    
    @swagger_auto_schema(
        operation_id='设置教练辅导记录状态',
        operation_summary='设置教练辅导记录状态为已填写',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导记录id'),
            }
        ),
        tags=['辅导管理相关']
    )
    @action(methods=['post'], detail=False, url_path='set_coach_record_status')
    def set_coach_record_status(self, request, *args, **kwargs):
        try:
            interview_id = request.data.get('interview_id')
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导记录不存在')
        except Exception as e:
            return parameter_error_response(str(e))

        interview.coach_record_status = 1
        interview.save()

        return success_response()




