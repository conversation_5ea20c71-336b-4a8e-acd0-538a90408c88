import pendulum
from django.db import transaction
from rest_framework import serializers
from datetime import datetime, timedelta
from django.db.models import Q

from utils.messagecenter.center import push_v2_message
from wisdom_v2.common import grow_public
from utils.utc_date_time import datetime_change_utc
from wisdom_v2.common import schedule_public
from wisdom_v2.common import interview_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.models import ProjectInterview, ProjectMember, Coach, PublicAttr, \
    InterviewRecordTemplate, PowerTag, Schedule, WorkWechatUser, ProjectCoach, CoachOffer
from utils.messagecenter.getui import send_work_wechat_coach_notice
from wisdom_v2.enum.project_interview_enum import ProjectInterviewRecordCompleteEnum, ProjectInterviewPlaceCategoryEnum, \
    ProjectInterviewTypeEnum, InterviewRecordTypeEnum, ProjectInterviewPlaceTypeEnum, InterviewSubjectEnum
from utils.api_response import WisdomValidation<PERSON>rror
from wisdom_v2.enum.service_content_enum import Coach<PERSON><PERSON><PERSON><PERSON>, ScheduleTypeEnum, CoachOfferStatusEnum, \
    OneToOneMatchTypeEnum
from utils import task
from wisdom_v2.models_file import ChemicalInterview2Coach, StakeholderInterview
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ADMIN_INTERVIEW_NOT_START, ATTR_STATUS_INTERVIEW_CANCEL, ADMIN_INTERVIEW_CANCEL, \
    ADMIN_INTERVIEW_ONGOING, ADMIN_INTERVIEW_END, ADMIN_PERSONAL_USER, ADMIN_PROJECT_USER, ADMIN_COACH_AGREE, \
    ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CONFIRM


class ProjectInterviewListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    name = serializers.CharField(source='public_attr.target_user.name', help_text='被教练者/利益相关者用户名',
                                 read_only=True)
    true_name = serializers.CharField(source='public_attr.target_user.cover_name', help_text='被教练者/利益相关者姓名')
    topic = serializers.SerializerMethodField(help_text='辅导主题', read_only=True)
    coach_name = serializers.CharField(source='public_attr.user.cover_name', help_text='教练姓名', read_only=True)
    project_name = serializers.CharField(source='public_attr.project.full_name', help_text='项目名称', read_only=True)
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M',
                                           help_text='辅导开始时间', read_only=True)
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M', help_text='辅导结束时间',
                                         read_only=True)
    times = serializers.CharField(help_text='预约时长', read_only=True)
    power_tag = serializers.SerializerMethodField(help_text='能力标签', read_only=True)
    interview_number = serializers.SerializerMethodField(help_text='辅导次数', read_only=True)
    interview_status = serializers.SerializerMethodField(help_text='辅导状态 1-未开始 2-进行中 3-完成 4-取消', read_only=True)
    interview_record_status = serializers.SerializerMethodField(help_text='辅导记录状态', read_only=True)
    close_reason = serializers.CharField(help_text='取消原因', read_only=True)
    relation_between_project_member = serializers.IntegerField(help_text='与被教练者关系', read_only=True)
    place_category = serializers.IntegerField(help_text='辅导类型 1-线上一对一，4-线下一对一， 3-线下集体辅导', read_only=True)
    type = serializers.IntegerField(help_text='辅导类型 1: 正式约谈 2: 企业面试 3:化学面试 4:利益相关者访谈', read_only=True)
    project_member_name = serializers.SerializerMethodField(help_text='利益相关者页面被教练者姓名', read_only=True)
    relation = serializers.SerializerMethodField(help_text='利益相关者与被教练者上下级关系 1-上级，2-平级， 3-下级',
                                                 read_only=True)
    interview_time = serializers.SerializerMethodField(help_text='辅导时间', read_only=True)
    chemical_interview_status = serializers.SerializerMethodField(help_text='化学面谈结果')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')
    interview_type_describe = serializers.SerializerMethodField(help_text='辅导类型描述')

    # 能力标签
    # 预约时长

    class Meta:
        model = ProjectInterview
        fields = ['id', 'name', 'coach_name', 'start_time', 'end_time', 'times', 'power_tag', 'interview_number',
                  'interview_status', 'interview_record_status', 'close_reason', 'relation_between_project_member',
                  'place_category', 'project_member_name', 'relation', 'project_name', 'true_name', 'record_type',
                  'topic', 'interview_time', 'chemical_interview_status', 'type', 'group_coach_type', 'interview_subject',
                  'interview_type_describe']

    def get_interview_type_describe(self, obj):
        return interview_public.get_interview_type_describe(obj)

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_topic(self, obj):
        return obj.message_topic

    def get_chemical_interview_status(self, obj):
        chemical_interview = obj.chemical_interview.filter(deleted=False).first()
        if not chemical_interview:
            return None
        return chemical_interview.chemical_interview_status

    def get_interview_time(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '~' + obj.public_attr.end_time.strftime('%H:%M')

    def get_power_tag(self, obj):
        if obj.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
            power_tag = PowerTag.objects.filter(
                group_coach__start_course_time=obj.public_attr.start_time,
                group_coach__end_course_time=obj.public_attr.end_time, group_coach__theme=obj.topic,
                group_coach__deleted=False, deleted=False,
                group_coach__project_bundle__project_member__user=obj.public_attr.target_user,
                group_coach__project_bundle__project=obj.public_attr.project)
            if power_tag.exists():
                power_tag = power_tag.values_list('tag', flat=True)
                power_tag = list(power_tag)
                return power_tag
        return '--'

    def get_project_member_name(self, obj):
        if obj.type == 4:
            stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
            if stakeholder_interview:
                return stakeholder_interview.stakeholder_interview_module.project_member.user.cover_name
            else:
                cancel_stakeholder_interview = obj.cancel_stakeholder_interview.filter(deleted=False).first()
                if not cancel_stakeholder_interview:
                    return ''
                stakeholder_interview = cancel_stakeholder_interview.stakeholder_interview
                return stakeholder_interview.stakeholder_interview_module.project_member.user.cover_name
        return ''

    def get_relation(self, obj):
        if obj.type == 4:
            stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
            if stakeholder_interview:
                return stakeholder_interview.project_interested.relation
            else:
                cancel_stakeholder_interview = obj.cancel_stakeholder_interview.filter(deleted=False).first()
                if not cancel_stakeholder_interview:
                    return ''
                stakeholder_interview = cancel_stakeholder_interview.stakeholder_interview
                return stakeholder_interview.project_interested.relation
        return ''

    def get_interview_number(self, obj):
        return interview_public.get_interview_number_by_obj(obj)

    def get_interview_record_status(self, obj):
        return get_interview_record_status_by_obj(obj)

    def get_interview_status(self, obj):
        return get_interview_status_by_obj(obj)


class ModifyProjectInterviewSerializer(serializers.ModelSerializer):
    coach_name = serializers.CharField(source='public_attr.user.cover_name', help_text='教练姓名', read_only=True)
    coach_user_id = serializers.CharField(source='public_attr.user_id', help_text='教练id', read_only=True)
    start_interview_time = serializers.DateTimeField(
        source='public_attr.start_time', format='%H:%M', help_text='辅导开始时间', read_only=True)
    end_interview_time = serializers.DateTimeField(
        source='public_attr.end_time', format='%H:%M', help_text='辅导结束时间', read_only=True)
    interview_date = serializers.DateTimeField(
        source='public_attr.start_time', format='%Y-%m-%d', help_text='辅导结束时间', read_only=True)
    interview_obj = serializers.SerializerMethodField(help_text='辅导对象 1-被教练者 2-利益相关者')
    stakeholder_name = serializers.SerializerMethodField(help_text='利益相关者姓名')
    stakeholder_user_id = serializers.SerializerMethodField(help_text='利益相关者id')
    coachee_name = serializers.SerializerMethodField(help_text='被教练者姓名')
    project_member_id = serializers.SerializerMethodField(help_text='被教练者项目id')
    interview_record_status = serializers.SerializerMethodField(help_text='辅导记录状态')


    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_interview_info(self, obj):
        if 'stakeholder_interview' not in self._cache:
            stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
            self._cache['stakeholder_interview'] = stakeholder_interview
        return self._cache

    def get_interview_record_status(self, obj):
        return get_interview_record_status_by_obj(obj, describe=True)

    def get_stakeholder_name(self, obj):
        interview_info = self.calculate_interview_info(obj)
        stakeholder_interview = interview_info.get('stakeholder_interview')
        if stakeholder_interview:
            return obj.public_attr.target_user.cover_name
        return None

    def get_stakeholder_user_id(self, obj):
        interview_info = self.calculate_interview_info(obj)
        stakeholder_interview = interview_info.get('stakeholder_interview')
        if stakeholder_interview:
            return obj.public_attr.target_user_id
        return None

    def get_coachee_name(self, obj):
        interview_info = self.calculate_interview_info(obj)
        stakeholder_interview = interview_info.get('stakeholder_interview')
        if stakeholder_interview:
            return stakeholder_interview.project_interested.master.cover_name

        else:
            return obj.public_attr.target_user.cover_name

    def get_project_member_id(self, obj):
        interview_info = self.calculate_interview_info(obj)
        stakeholder_interview = interview_info.get('stakeholder_interview')
        if stakeholder_interview:
            member_user_id = stakeholder_interview.project_interested.master_id
        else:
            member_user_id = obj.public_attr.target_user_id

        project_member = ProjectMember.objects.filter(
            user_id=member_user_id, deleted=False, project_id=obj.public_attr.project_id).first()
        if project_member:
            return project_member.id
        return


    def get_interview_obj(self, obj):
        interview_info = self.calculate_interview_info(obj)
        stakeholder_interview = interview_info.get('stakeholder_interview')
        # 1-被教练者 2-利益相关者
        if stakeholder_interview:
            return 2
        return 1


    class Meta:
        model = ProjectInterview
        fields = ['start_interview_time', 'end_interview_time', 'interview_date', 'type', 'place_type', 'place',
                  'interview_subject', 'times', 'coachee_name', 'stakeholder_name', 'coach_name', 'project_member_id',
                  'coach_user_id', 'stakeholder_user_id', 'interview_obj', 'interview_record_status', 'id']


class BackendProjectInterviewCreateSerializer(serializers.ModelSerializer):
    project_member_id = serializers.IntegerField(write_only=True, help_text='被教练者id', required=True)
    user_id = serializers.IntegerField(write_only=True, help_text='教练用户id', required=True)
    interview_date = serializers.CharField(write_only=True, help_text='辅导日期', required=True)
    times = serializers.CharField(write_only=True, help_text='辅导时长', required=True)
    start_interview_time = serializers.CharField(write_only=True, help_text='辅导开始时间', required=True)
    end_interview_time = serializers.CharField(write_only=True, help_text='辅导结束时间', required=True)
    stakeholder_id = serializers.IntegerField(write_only=True, help_text='利益相关者id', required=False)
    interview_obj = serializers.IntegerField(write_only=True, help_text='辅导对象 1-被教练者 2-利益相关者', required=True)
    type = serializers.IntegerField(write_only=True, help_text='教练形式 1-线上一对一 2-线下一对一', required=True)
    place_type = serializers.IntegerField(write_only=True, required=False, help_text='辅导地址类型')
    interview_subject = serializers.IntegerField(write_only=True, required=False, help_text='辅导形式')
    place = serializers.CharField(write_only=True, required=False, help_text='辅导地址')

    class Meta:
        model = ProjectInterview
        fields = ['project_member_id', 'user_id', 'interview_date', 'times', 'start_interview_time',
                  'end_interview_time', 'stakeholder_id', 'interview_obj', 'type', 'place_type', 'place',
                  'interview_subject']

    def create(self, validated_data):
        type = validated_data['type']
        place_type = validated_data.get('place_type', ProjectInterviewPlaceTypeEnum.online.value)
        place = validated_data.get('place')
        interview_subject = validated_data.get('interview_subject', InterviewSubjectEnum.regular.value)
        coach_user_id = validated_data['user_id']
        start_time = f"{validated_data['interview_date']} {validated_data['start_interview_time']}"
        if validated_data['end_interview_time'] == '24:00':
            tmp_end_time = datetime.strptime(validated_data['interview_date'], '%Y-%m-%d')
            tmp_end_time = tmp_end_time + timedelta(days=1)
            end_time = tmp_end_time.strftime('%Y-%m-%d %H:%M')
        else:
            end_time = f"{validated_data['interview_date']} {validated_data['end_interview_time']}"
        times = int(validated_data['times'])
        project_member_id = validated_data['project_member_id']
        project_member = ProjectMember.objects.filter(pk=project_member_id, deleted=False).first()
        if not project_member:
            raise WisdomValidationError('账号错误')
        coach = Coach.objects.filter(user_id=coach_user_id, deleted=False).first()
        if not coach:
            raise WisdomValidationError('未找到教练')
        project = project_member.project
        if type == ProjectInterviewTypeEnum.formal_interview:  # 线上一对一辅导
            project_bundle = project_member.project_bundle.filter(deleted=False).first()
            if not project_bundle:
                raise WisdomValidationError('当前被教练者未配置服务内容')
            if not project_bundle.one_to_one_coach.filter(deleted=False).exists():
                raise WisdomValidationError('当前被教练者未配置1对1辅导模块')
            if not project_bundle.one_to_one_coach.filter(deleted=False, type=CoachTypeEnum.online.value).exists():
                raise WisdomValidationError('当前被教练者服务内容不包含线上1对1辅导')

            if project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
                if not ProjectCoach.objects.filter(
                        project=project, coach__user_id=coach_user_id, member_id=project_member.user_id,
                        project_group_coach__isnull=True, deleted=False).exists():
                    raise WisdomValidationError('当前被教练者未与当前教练匹配')
            if project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
                if not ProjectCoach.objects.filter(project=project, deleted=False, coach__user_id=coach_user_id,
                                                   project_group_coach__isnull=True).exists():
                    raise WisdomValidationError('当前项目未匹配该教练')
            result, available_time = interview_public.check_member_interview_times(project_member, times)
            if not result:
                raise WisdomValidationError('当前被教练者目前没有可用的教练辅导时长')

            # 判断时间是否可用
            start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, time_status=True)
            status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
            if not status:
                raise WisdomValidationError('预约失败，所选时间教练已存在其他日程')

            with transaction.atomic():
                public_attr = PublicAttr.objects.create(start_time=start_date_time, end_time=end_date_time,
                                                        project_id=project_member.project_id,
                                                        user_id=coach_user_id, target_user_id=project_member.user_id,
                                                        type=ATTR_TYPE_INTERVIEW, status=ATTR_STATUS_INTERVIEW_CONFIRM)
                Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title='教练辅导')

                project_interview = ProjectInterview.objects.create(
                    public_attr_id=public_attr.pk, type=ProjectInterviewTypeEnum.formal_interview,
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                    record_type=InterviewRecordTypeEnum.questionnaire.value,
                    place_type=place_type, place=place, interview_subject=interview_subject,
                    is_coach_agree=True,  # B端默认教练同意
                    times=times, topic='教练辅导', coachee_topic='教练辅导')

            start_time = datetime_change_utc(start_time)
            if pendulum.now() < start_time:

                is_send = grow_public.send_interview_tips.delay(project_interview.id)
                # 没有发送成长目标提醒，就发送普通消息
                if not is_send:
                    interview_time = project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') +\
                                    '-' + project_interview.public_attr.end_time.strftime('%H:%M')
                    push_v2_message.delay(coach.user, 'interview_add', param={'interview_time': interview_time,
                                                                            'target_user': project_member.user.cover_name}, project_id=project.id)
                    push_v2_message.delay(project_member.user, 'interview_add', param={'interview_time': interview_time,
                                                                        'target_user': coach.user.cover_name}, project_id=project.id)

                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=coach.user.id,
                    deleted=False
                ).first()
                task.send_coach_add_interview_message.apply_async(kwargs=dict(
                    interview_id=project_interview.pk, project_id=project_interview.public_attr.project.pk),
                    countdown=3, expires=10)
                if work_wechat_user:
                    company = project_interview.public_attr.project.company
                    company_name = company.real_name
                    if isinstance(public_attr.start_time, str):
                        date = '{}-{}'.format(
                            datetime_change_utc(public_attr.start_time).strftime('%Y-%m-%d %H:%M'),
                            datetime_change_utc(public_attr.end_time).strftime('%H:%M'))
                    else:
                        date = '{}-{}'.format(
                            public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                            public_attr.end_time.strftime('%H:%M'))

                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'coach_add_interview',
                        company=company_name,
                        coachee_name=project_member.user.cover_name,
                        date=date,
                        coach_id=project_interview.public_attr.user.id,
                        project_id=project_interview.public_attr.project_id,
                        project_name=project_interview.public_attr.project.name,
                        coachee_id=project_interview.public_attr.target_user.id,
                        coach_name=project_interview.public_attr.user.cover_name,
                        content_item=[
                            {
                                "key": "客户名称", "value": project_member.user.cover_name
                            },
                            {
                                "key": "所属企业", "value": company_name
                            },
                            {
                                "key": "所属项目", "value": project_member.project.name
                            },
                            {
                                "key": "辅导时长", "value": '{}分钟'.format(project_interview.times)
                            },
                            {
                                "key": "辅导时间", "value": date,
                            },
                        ])
                    time_delta = end_date_time - start_date_time
                    # 线上辅导创建会议
                    if place_type == ProjectInterviewPlaceTypeEnum.online.value:
                        task.create_project_interview_meeting.delay(
                            project_interview.id, work_wechat_user.wx_user_id, '教练辅导',
                            int(start_date_time.timestamp()), time_delta.seconds)
        elif type == ProjectInterviewTypeEnum.chemical_interview:  # 化学面谈
            # if ChemicalInterview2Coach.objects.filter(
            #         chemical_interview_module__project_member=project_member,
            #         chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback,
            #         interview__isnull=False, deleted=False).exists():
            #     raise WisdomValidationError('当前用户有化学面谈尚未反馈，请反馈后再预约')
            chemical_interview = ChemicalInterview2Coach.objects.filter(
                chemical_interview_module__project_member=project_member, coach=coach,
                chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback,
                chemical_interview_module__start_time__lte=datetime.now().date(),
                chemical_interview_module__end_time__gte=datetime.now().date(),
                chemical_interview_module__deleted=False,
                interview__isnull=True, deleted=False
            ).first()
            if not chemical_interview:
                raise WisdomValidationError('未找到可预约的化学面谈配置')
            # if int(chemical_interview.chemical_interview_module.duration * 60) != int(times):
            #     raise WisdomValidationError('化学面谈时长错误')
            # 查看当前教练是否已约满
            coach_offer = CoachOffer.objects.filter(project_offer__project_id=project.id, coach=coach,
                                                    status=CoachOfferStatusEnum.joined, deleted=False,
                                                    project_offer__deleted=False).first()
            if coach_offer:
                count = ChemicalInterview2Coach.objects.filter(coach=coach, interview__isnull=False, deleted=False,
                                                               interview__public_attr__project_id=project.id). \
                    exclude(chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
                if coach_offer.max_customer_count and count >= coach_offer.max_customer_count:
                    raise WisdomValidationError('当前教练化学面谈已约满')
            start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, time_status=True)
            status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
            if not status:
                raise WisdomValidationError('教练该时间段时间已预约')
            with transaction.atomic():
                public_attr = PublicAttr.objects.create(start_time=start_date_time, end_time=end_date_time,
                                                        project=project_member.project,
                                                        user_id=coach_user_id, target_user_id=project_member.user_id,
                                                        type=ATTR_TYPE_INTERVIEW, status=ATTR_STATUS_INTERVIEW_CONFIRM)
                Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview,
                                        title='进行初步的沟通')
                project_interview = ProjectInterview(
                    public_attr_id=public_attr.pk, type=ProjectInterviewTypeEnum.chemical_interview,
                    place_type=place_type, place=place, interview_subject=interview_subject,
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one, times=times,
                    record_type=InterviewRecordTypeEnum.questionnaire, topic='进行初步的沟通')
                project_interview.save()
                chemical_interview.interview = project_interview
                chemical_interview.save()
            start_time = datetime_change_utc(start_time)
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=coach_user_id,
                deleted=False
            ).first()
            if work_wechat_user:
                # 创建化学面谈会议
                time_delta = end_date_time - start_date_time
                # 线上辅导创建会议
                if place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    task.create_project_interview_meeting.delay(
                        project_interview.id, work_wechat_user.wx_user_id, '化学面谈',
                        int(start_date_time.timestamp()), time_delta.seconds)

                # 发送消息提醒
                if pendulum.now() < start_time:
                    company = project_interview.public_attr.project.company
                    company_name = company.real_name
                    send_work_wechat_coach_notice.apply_async(kwargs=dict(
                        user=work_wechat_user.wx_user_id, content_type='coachee_add_chemical_interview',
                        coachee_name=public_attr.target_user.cover_name,
                        company=company_name,
                        interview_id=project_interview.id,
                        coach_id=project_interview.public_attr.user.id,
                        project_id=project_interview.public_attr.project_id,
                        project_name=project_interview.public_attr.project.name,
                        coachee_id=project_interview.public_attr.target_user.id,
                        coach_name=project_interview.public_attr.user.cover_name,
                        content_item=[
                            {
                                "key": "客户名称", "value": public_attr.target_user.cover_name
                            },
                            {
                                "key": "所属企业", "value": company_name
                            },
                            {
                                "key": "所属项目", "value": project_interview.public_attr.project.name
                            }
                        ]
                    ), countdown=30, expires=360)
        elif type == ProjectInterviewTypeEnum.stakeholder_interview:  # 利益相关者访谈
            stakeholder_id = validated_data.get('stakeholder_id')
            if not stakeholder_id:
                raise WisdomValidationError('未找到利益相关者')
            stakeholder_interview = StakeholderInterview.objects.filter(
                deleted=False, stakeholder_interview_module__project_member=project_member,
                project_interested__interested_id=stakeholder_id, interview__isnull=True,
                stakeholder_interview_module__start_date__lte=datetime.now().date(),
                stakeholder_interview_module__end_date__gte=datetime.now().date(),
                stakeholder_interview_module__deleted=False
            ).first()
            if not stakeholder_interview:
                raise WisdomValidationError('未找到可预约的利益相关者访谈配置')
            start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, time_status=True)
            status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
            if not status:
                raise WisdomValidationError('教练该时间段时间已预约')
            exists_stakeholder_interview = ProjectInterview.objects.filter(
                Q(public_attr__start_time__range=(start_date_time, end_date_time)) |  # 查询开始时间在当前预约时间范围内的Interview对象
                Q(public_attr__end_time__range=(start_date_time, end_date_time)) |  # 查询结束时间在当前预约时间范围内的Interview对象
                Q(public_attr__start_time__lte=start_date_time, public_attr__end_time__gte=end_date_time),
                # 查询开始时间早于当前预约时间且结束时间晚于当前预约时间的Interview对象。
                type=ProjectInterviewTypeEnum.stakeholder_interview, deleted=False,
                public_attr__target_user_id=stakeholder_interview.project_interested.interested_id,
            ).exclude(
                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
            if exists_stakeholder_interview.exists():
                raise WisdomValidationError(f'当前时间已预约'
                                            f'{exists_stakeholder_interview.first().public_attr.user.cover_name}教练,'
                                            f'请选择其他时间预约')
            topic = f"{project_member.user.cover_name}的行为反馈"
            with transaction.atomic():
                public_attr = PublicAttr.objects.create(
                    start_time=start_date_time, end_time=end_date_time, project=project_member.project,
                    user_id=coach.user_id, target_user_id=stakeholder_interview.project_interested.interested_id,
                    type=ATTR_TYPE_INTERVIEW, status=ATTR_STATUS_INTERVIEW_CONFIRM)
                Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)
                project_interview = ProjectInterview(
                    public_attr_id=public_attr.pk, type=ProjectInterviewTypeEnum.stakeholder_interview,
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one, times=times,
                    place_type=place_type, place=place, interview_subject=interview_subject,
                    record_type=InterviewRecordTypeEnum.questionnaire, coachee_topic=topic, topic=topic)
                project_interview.save()
                stakeholder_interview.interview = project_interview
                stakeholder_interview.save()

                # 线上辅导创建会议
                if place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    # 创建利益相关者访谈会议
                    work_wechat_user = WorkWechatUser.objects.filter(
                        wx_user_id__isnull=False, user_id=coach_user_id, deleted=False).first()
                    if work_wechat_user:
                        time_delta = end_date_time - start_date_time
                        task.create_project_interview_meeting.delay(
                            project_interview.id, work_wechat_user.wx_user_id, '利益相关者访谈',
                            int(start_date_time.timestamp()), time_delta.seconds)

            start_time = datetime_change_utc(start_time)
            if pendulum.now() < start_time:
                # 给教练，利益相关者，项目运营发送通知
                task.send_stakeholder_create_interview_notice.apply_async(
                    kwargs=dict(stakeholder_interview=stakeholder_interview), countdown=5)
        return 'success'

def get_interview_template(project_interview_id, role=None):
    if not role:
        project_interview = ProjectInterview.objects.filter(pk=project_interview_id, deleted=False).first()
        # user = project_interview.public_attr.target_user
        # project = project_interview.public_attr.project
        # project_member = ProjectMember.objects.filter(user=user, project=project, deleted=False,
        #                                               role=ProjectMemberRoleEnum.coachee.value).first()
        # project_bundle = project_member.project_bundle.filter(deleted=False).first()
        # group_coach = project_bundle.coach_group.filter(
        #     deleted=False,
        #     theme=project_interview.topic, start_course_time=project_interview.public_attr.start_time,
        #     end_course_time=project_interview.public_attr.end_time, course_place=project_interview.place).first()
        group_coach = project_interview.coach_group_module.filter(deleted=False).first()
        interview_template = group_coach.inter_view_record_template
        return interview_template.pk
    else:
        if role == 3:
            interview_template = InterviewRecordTemplate.objects.filter(name='见习用2').first()
        else:
            interview_template = InterviewRecordTemplate.objects.filter(name='见习用1').first()
        return interview_template.pk


# 新增的辅导详情列表序列化
# 管理后台一级目录下辅导明细的辅导列表使用
class ProjectInterviewDetailListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    coachee_name = serializers.CharField(source='public_attr.target_user.cover_name', help_text='被教练者/利益相关者姓名')
    coach_name = serializers.CharField(source='public_attr.user.cover_name', help_text='教练姓名', read_only=True)
    created_time = serializers.DateTimeField(source='created_at', format='%Y-%m-%d %H:%M:%S', help_text='创建时间')
    interview_number = serializers.SerializerMethodField(help_text='辅导次数', read_only=True)
    interview_status = serializers.SerializerMethodField(help_text='辅导状态 1-未开始 2-进行中 3-完成 4-取消', read_only=True)
    interview_record_status = serializers.SerializerMethodField(help_text='辅导记录状态', read_only=True)
    place_category = serializers.IntegerField(help_text='辅导类型 1-线上一对一，4-线下一对一， 3-线下集体辅导', read_only=True)
    times = serializers.SerializerMethodField(help_text='预约时长')
    interview_time = serializers.SerializerMethodField(help_text='辅导时间', read_only=True)
    target_progress_score = serializers.SerializerMethodField(help_text='有效度')
    harvest_score = serializers.SerializerMethodField(help_text='投入度')
    satisfaction_score = serializers.SerializerMethodField(help_text='满意度')
    recommend_score = serializers.SerializerMethodField(help_text='推荐度')
    coachee_type = serializers.SerializerMethodField(help_text='用户类型')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    topic = serializers.SerializerMethodField(help_text='辅导主题')
    payer_amount = serializers.SerializerMethodField(help_text='实付金额')
    refund_reason = serializers.SerializerMethodField(help_text='退款原因')
    order_state = serializers.SerializerMethodField(help_text='订单状态')
    activity_theme = serializers.SerializerMethodField(help_text='活动主题')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')
    business_order_id = serializers.SerializerMethodField(help_text='结算订单id')

    class Meta:
        model = ProjectInterview
        fields = [
            'id', 'coachee_name', 'coachee_topic', 'coach_name', 'times', 'interview_number',
            'interview_status', 'interview_record_status', 'place_category', 'project_name',
            'topic', 'interview_time', 'target_progress_score', 'harvest_score', 'satisfaction_score',
            'recommend_score', 'is_settlement', 'coachee_type', 'payer_amount', 'refund_reason', 'order_state',
            'activity_theme', 'group_coach_type', 'type', 'business_order_id', 'created_time', 'record_type']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._score_cache = {}

    def calculate_record_rating(self, obj):
        # 使用对象的ID作为缓存键
        obj_id = obj.id
        if obj_id not in self._score_cache:
            self._score_cache[obj_id] = interview_public.get_interview_record_rating(obj)
        return self._score_cache[obj_id]

    def get_business_order_id(self, obj):
        business_order = interview_public.get_interview_to_business_order(obj)
        if business_order:
            return str(business_order.pk)
        return

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_activity_theme(self, obj):
        activity_interview = obj.activity_interview.filter(deleted=False).first()
        return activity_interview.activity.theme if activity_interview else None

    def get_order_state(self, obj):
        if obj.order:
            if obj.order.status == OrderStatusEnum.pending_pay and obj.order.deleted == False:
                return constant.ADMIN_ORDER_STATE_PENDING_PAY
            elif obj.order.status == OrderStatusEnum.pending_pay and obj.order.deleted == True:
                return constant.ADMIN_ORDER_STATE_CLOSURE
            elif obj.order.status == OrderStatusEnum.paid and obj.order.deleted == False:
                return constant.ADMIN_ORDER_STATE_COMPLETE
            elif obj.order.status in [OrderStatusEnum.under_refund, OrderStatusEnum.refunded]:
                return constant.ADMIN_ORDER_STATE_REFUND
        return

    def get_refund_reason(self, obj):
        return obj.close_reason

    def get_payer_amount(self, obj):
        if obj.order:
            return round(obj.order.payer_amount / 100, 2)

    def get_project_name(self, obj):
        if obj.public_attr.project:
            return obj.public_attr.project.full_name

    def get_topic(self, obj):
        return obj.message_topic

    def get_coachee_type(self, obj):
        if obj.public_attr.project:
            return ADMIN_PROJECT_USER
        return ADMIN_PERSONAL_USER

    def get_times(self, obj):
        return round(int(obj.times) / 60, 1)

    def get_target_progress_score(self, obj):
        target_progress_score, harvest_score, satisfaction_score, coach_score = self.calculate_record_rating(obj)
        return target_progress_score if target_progress_score else '-'

    def get_harvest_score(self, obj):
        target_progress_score, harvest_score, satisfaction_score, coach_score = self.calculate_record_rating(obj)

        return harvest_score if harvest_score else '-'

    def get_satisfaction_score(self, obj):
        target_progress_score, harvest_score, satisfaction_score, coach_score = self.calculate_record_rating(obj)
        return satisfaction_score if satisfaction_score else '-'

    def get_recommend_score(self, obj):
        target_progress_score, harvest_score, satisfaction_score, coach_score = self.calculate_record_rating(obj)
        return coach_score if coach_score else '-'

    def get_interview_time(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '~' + obj.public_attr.end_time.strftime('%H:%M')

    def get_interview_number(self, obj):
        return interview_public.get_interview_number_by_obj(obj, describe=True)

    def get_interview_record_status(self, obj):
        return get_interview_record_status_by_obj(obj, describe=True)

    def get_interview_status(self, obj):
        return get_interview_status_by_obj(obj, describe=True)


def get_interview_record_status_by_obj(obj, describe=False):

    if not obj.coach_record_status and not obj.coachee_record_status:
        return ProjectInterviewRecordCompleteEnum.all_unfinished.value if not describe else '双方未完成'
    elif not obj.coach_record_status and obj.coachee_record_status:
        return ProjectInterviewRecordCompleteEnum.coach_unfinished.value if not describe else '教练未完成'
    elif obj.coach_record_status and not obj.coachee_record_status:
        return ProjectInterviewRecordCompleteEnum.member_unfinished.value if not describe else '客户未完成'
    elif obj.coach_record_status and obj.coachee_record_status:
        return ProjectInterviewRecordCompleteEnum.finished.value if not describe else '已完成'
    return 0


def get_interview_status_by_obj(obj, describe=False):

    if obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
        return ADMIN_INTERVIEW_CANCEL if not describe else '取消'

    if not obj.is_coach_agree:
        return ADMIN_COACH_AGREE if not describe else '教练未确认'

    now = datetime.now()
    if obj.public_attr.start_time > now and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        return ADMIN_INTERVIEW_NOT_START if not describe else '未开始'
    if obj.public_attr.start_time < now < obj.public_attr.end_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        return ADMIN_INTERVIEW_ONGOING if not describe else '进行中'
    if obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.end_time < now:
        return ADMIN_INTERVIEW_END if not describe else '已结束'

    return 0

