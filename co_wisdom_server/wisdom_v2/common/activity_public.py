from datetime import datetime
from decimal import Decimal

from django.conf import settings
from django.forms import model_to_dict

from utils import qr_code
from utils import utc_date_time
from utils.aliyun_sls_log import AliyunSlsLogLayout
from wisdom_v2.app_views import app_order_action
from wisdom_v2.common import coach_public, schedule_public, activity_coach_public
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum, ActivityCoachStatusEnum, UserInviteTypeEnum, \
    ScheduleApplyTypeEnum
from wisdom_v2.models import UserInviteRecord, Order
from wisdom_v2.models_file import Activity, ActivityCoach, ActivityInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


def get_status_display(activity):
    """
    获取活动状态
    :param activity: 活动对象
    :return: 活动状态 1-未开始 2-进行中 3-已结束 4-已删除
    """
    state = 1
    if activity.deleted:
        state = 4
    elif activity.start_date > datetime.now().date():
        state = 1
    elif activity.start_date <= datetime.now().date() <= activity.end_date:
        state = 2
    elif activity.end_date < datetime.now().date():
        state = 3
    return state


def get_coach_content(activity, is_user_query=False):
    """
    获取活动教练信息
    :param activity: 活动对象
    :param is_user_query: 是否是用户查询
    :return: 活动教练信息
    """

    all_activity_coach = activity.activity_coach.filter(deleted=False)

    # 如果是小程序用户查看，只展示接受邀请和免邀请的教练。
    if is_user_query:
        all_activity_coach = all_activity_coach.filter(
            status__in=[ActivityCoachStatusEnum.not_invitation.value, ActivityCoachStatusEnum.joined.value])

    coach_data = []
    for activity_coach in all_activity_coach.order_by('?').all():
        resume = activity_coach.resume
        domain = resume.coach_domain[:3] if resume.coach_domain else []
        data = {
            'id': activity_coach.coach_id,
            'user_id': activity_coach.coach.user_id,
            'true_name': activity_coach.coach.user.cover_name,
            'personal_name': activity_coach.coach.personal_name,
            'gender': activity_coach.coach.user.gender,
            'domain': domain,
            'price': 0,
            'working_years': resume.working_years if resume else None,
            'coach_auth': resume.coach_auth if resume else None,
            'resume_id': resume.id if resume else None,
            'city': activity_coach.coach.city,
            'status': activity_coach.status,
            'activity_coach_id': str(activity_coach.id)

        }
        if is_user_query:
            head_image_url = resume.head_image_url if resume.head_image_url else activity_coach.coach.user.head_image_url,
            data['true_name'] = activity_coach.coach.personal_name
            data['head_image_url'] = head_image_url
            data['payable_price'] = activity_coach.coach.display_price
            data['interview_count'] = (coach_public.get_coach_to_c_interview_count(activity_coach.coach.user_id)
                                       + activity_coach.additional_count)

            # 活动可预约辅导开始日期小于当前日期，可预约辅导开始日期更新为当前日期
            if activity.interview_start_date < datetime.now().date():
                interview_start_date = datetime.now().date()
            else:
                interview_start_date = activity.interview_start_date
            is_eight_clock_time = getattr(activity, 'is_eight_clock_time', False)

            all_schedule_data, all_schedule_hour = schedule_public.get_schedule_time_list(
                activity_coach.coach.user_id, interview_start_date, activity.interview_end_date,
                apply_type=ScheduleApplyTypeEnum.activity.value, is_eight_clock_time=is_eight_clock_time)
            data['is_remaining_schedule_hour'] = bool(all_schedule_hour)
        else:
            # 获取活动时间段内所有日程
            start_date = utc_date_time.datetime_change_utc(activity_coach.activity.start_date).date()
            end_date = utc_date_time.datetime_change_utc(activity_coach.activity.end_date).date()
            schedule_data = schedule_public.get_coach_all_schedule(start_date, end_date, activity_coach.coach)

            # 转成数据格式
            schedule_content = []
            for item in schedule_data:
                # 日期相同只展示一个年月日
                if item.get('start_time').date() == item.get('end_time').date():
                    str_date = f"{item.get('start_time').strftime('%Y.%m.%d %H:%M')} ~ {item.get('end_time').strftime('%H:%M')}"
                else:
                    str_date = f"{item.get('start_time').strftime('%Y.%m.%d %H:%M')} ~ {item.get('end_time').strftime('%Y.%m.%d %H:%M')}"
                schedule_type = item.get('type')
                if schedule_type == ScheduleTypeEnum.available.value:
                    title = "可预约时段"
                else:
                    title = '忙碌'
                schedule_content.append({"title": title, "date": str_date})
            data['schedule_content'] = schedule_content
        coach_data.append(data)
    return coach_data


def get_activity_qrcode(activity):
    """
    获取活动二维码
    """
    page = 'pages_coach/activity_landing/activity_coach_list'
    scene = f'activity_id={activity.id}'

    url = qr_code.get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
    return url


def create_activity(validated_data):
    """
    根据验证过的数据创建活动实例。

    :param validated_data: 包含活动数据的字典。
    :return: 创建的活动实例。
    """

    # 从数据中移除教练信息
    activity_coach = validated_data.pop('activity_coach')

    # 将价格从字符串转换为以分为单位的整数
    price = validated_data.get('price')
    validated_data['price'] = int(Decimal(price) * Decimal('100'))

    # 创建活动实例
    instance = Activity.objects.create(**validated_data)

    # 绑定教练信息到活动
    activity_coach_public.add_activity_coach(instance, activity_coach)

    # 创建活动的邀请记录
    UserInviteRecord.objects.create(
        type=UserInviteTypeEnum.activity.value, referrer=None, object_id=str(instance.id))
    create_activity_invites(instance.id)

    return instance


def update_activity(instance, validated_data):
    """
    更新一个活动实例。

    :param instance: 要更新的活动实例。
    :param validated_data: 包含更新数据的字典。
    :return: 更新后的活动实例。
    """

    # 检查并处理活动的删除标志
    if 'deleted' in validated_data and validated_data['deleted']:
        instance.deleted = True
        instance.save()

        # 更新相关联的数据，标记为已删除
        ActivityCoach.objects.filter(activity_id=instance.id).update(deleted=True)
        ActivityInterview.objects.filter(activity_id=instance.id).update(deleted=True)
        UserInviteRecord.objects.filter(type=UserInviteTypeEnum.activity.value, object_id=str(instance.id)).update(
            deleted=True)
        return instance
    else:
        # 更新教练信息
        if 'activity_coach' in validated_data:
            activity_coach = validated_data.pop('activity_coach')
            old_coach_ids = list(instance.activity_coach.filter(deleted=False).values_list('coach_id', flat=True))

            # 教练状态如果发生变化，需要及时更新。
            coach_ids = []
            for item in activity_coach:
                status = item.get('status')
                coach_id = item.get('coach_id')
                # 教练是否已经绑定过活动
                if ActivityCoach.objects.filter(activity_id=instance.id, coach_id=coach_id, deleted=False).exists():
                    activity_coach_obj = ActivityCoach.objects.filter(
                        activity_id=instance.id, coach_id=coach_id, deleted=False).first()
                    # 活动状态不一致时需要更新
                    if activity_coach_obj.status != status:
                        activity_coach_obj.status = status
                        activity_coach_obj.save()
                coach_ids.append(coach_id)


            # 计算教练列表的差异，进行更新
            del_coach_list = list(set(old_coach_ids) - set(coach_ids))
            add_coach_list = list(set(coach_ids) - set(old_coach_ids))

            if del_coach_list:
                ActivityCoach.objects.filter(activity_id=instance.id, coach_id__in=del_coach_list).update(deleted=True)
            if add_coach_list:
                new_activity_coach = [item for item in activity_coach if item['coach_id'] in add_coach_list]
                activity_coach_public.add_activity_coach(instance, new_activity_coach)

        # 更新价格，转换为以分为单位的整数
        if 'price' in validated_data:
            price = validated_data.get('price')
            validated_data['price'] = Decimal(str(price)) * Decimal('100')

        # 更新指定的活动字段
        fields = [
            'theme', 'brief', 'type', 'start_date', 'end_date',
            'poster_image_url', 'model_image_url', 'guide_image_url',
            'limit_count', 'rule', 'bg_color', 'head_image_url', 'notes',
            'interview_start_date', 'interview_end_date', 'price', 'accum_limit_count'
        ]
        for field in fields:
            if field in validated_data:
                setattr(instance, field, validated_data[field])

        # 保存更新后的实例
        instance.save()
        
        create_activity_invites(instance.id)

        return instance


def refund_timeout_date_activity_order_all(data_str):
    """
    对于指定日期之前结束且已支付的活动订单执行超时自动退款操作。

    :param data_str: 用于筛选活动结束日期的日期字符串。
    :return: 包含执行退款操作失败的订单信息的列表。
    """

    # 查找所有符合条件的订单：活动结束日期早于指定日期、订单状态为已支付、未被删除
    orders = Order.objects.filter(
        activity__interview_end_date__lt=data_str, status=OrderStatusEnum.paid.value, deleted=False)

    error = []  # 用于收集退款操作失败的订单信息
    for order in orders:
        # 检查订单关联的辅导是否全部被删除或标记为取消状态
        if not order.interview.filter(deleted=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():
            # 尝试为订单创建退款操作
            is_success, msg = app_order_action.create_refund_order(order, '超时自动退款', refund_type='wechat')
            if not is_success:
                # 如果退款操作失败，记录错误信息
                error.append(f'订单：{order.order_no}超时自动退款失败，失败原因：{msg}')
            else:
                params = {
                    'url': "refund_timeout_date_activity_order_all",
                    'user_id': order.public_attr.user_id,
                    'true_name': order.public_attr.user.cover_name,
                    'params': model_to_dict(order),
                    'message': '订单退款'
                }
                AliyunSlsLogLayout().send_business_api_log(**params)
    return error


def create_activity_invites(activity_id):
    """
    创建活动邀请记录
    :param activity_id: 活动ID
    :return: 创建的邀请记录数量
    """
    # 默认用户ID列表
    default_user_ids = [33149, 32427, 33150, 33151, 33152]
    
    # 获取活动教练的用户ID
    activity = Activity.objects.get(id=activity_id)
    coach_ids = list(activity.activity_coach.filter(
        deleted=False,
        status__in=[ActivityCoachStatusEnum.not_invitation.value, ActivityCoachStatusEnum.joined.value]
    ).values_list('coach__user_id', flat=True))
    
    # 合并教练ID和默认用户ID
    all_user_ids = coach_ids + default_user_ids
    
    # 查询已存在的邀请记录
    existing_records = UserInviteRecord.objects.filter(
        type=UserInviteTypeEnum.activity.value,
        object_id=str(activity_id),
        referrer_id__in=all_user_ids
    ).values_list('referrer_id', flat=True)
    
    # 准备批量创建的记录
    new_records = [
        UserInviteRecord(
            type=UserInviteTypeEnum.activity.value,
            object_id=str(activity_id),
            referrer_id=user_id
        )
        for user_id in set(all_user_ids) - set(existing_records)
    ]
    
    # 批量创建记录
    if new_records:
        UserInviteRecord.objects.bulk_create(new_records)
    # print(f"Created {len(new_records)} new invite records for activity {activity_id}")
    return len(new_records)


