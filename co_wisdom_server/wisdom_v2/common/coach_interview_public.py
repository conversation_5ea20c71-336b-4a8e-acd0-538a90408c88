import datetime

import pendulum
from django.conf import settings
from django.db.models import Sum, Q

from utils import utc_date_time, excel_pubilc, aliyun
from wisdom_v2.app_views.app_coach_interview_actions import AppCoachInterviewDataSerializer
from wisdom_v2.common import interview_public
from wisdom_v2.enum.business_order_enum import WorkTypeEnum
from wisdom_v2.enum.coach_enum import NonPlatformInterviewPayTypeEnum, NonPlatformInterviewTypeEnum, \
    NonPlatformInterviewSourceEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewPlaceTypeEnum, ProjectInterviewTypeEnum
from wisdom_v2.models import GroupCoach, ProjectInterview
from wisdom_v2.models_file import PublicCoursesCoach
from wisdom_v2.models_file.coach import NonPlatformInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


def coach_project_interview_to_dict(project_interview):
    """
    根据项目辅导信息对象，构造并返回一个包含辅导详细信息的字典。
    包括辅导类型、支付类型、用户数量、辅导时间、开始时间、结束时间、议题、客户名称、客户邮箱等信息。

    :param project_interview: 项目辅导信息对象
    :return: 包含辅导详细信息的字典
    """
    if not project_interview:
        return {}

    public_attr = project_interview.public_attr

    # 根据订单信息判断支付类型（免费或付费）
    if project_interview.order_id and project_interview.order and project_interview.order.payer_amount == 0:
        pay_type = NonPlatformInterviewPayTypeEnum.free.value
    else:
        pay_type = NonPlatformInterviewPayTypeEnum.pay.value

    user_count = 1  # 默认有一位客户
    customer_name = None
    customer_email = None
    head_image_url = None
    customer_id = None
    project_id = None

    # 根据辅导地点类别判断辅导类型，并根据类型分别处理获取客户名称和客户邮箱
    if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one:
        interview_type = NonPlatformInterviewTypeEnum.personal.value
        target_user = public_attr.target_user
        customer_name = target_user.cover_name
        customer_email = target_user.email
        head_image_url = target_user.head_image_url
        customer_id = target_user.id
    else:
        interview_type = NonPlatformInterviewTypeEnum.group.value
        user_count = GroupCoach.objects.filter(deleted=False, interview_id=project_interview.id).count()
        if public_attr.project_id and public_attr.project:
            project = public_attr.project
            company = project.company
            customer_name = company.real_name
            customer_email = project.manager_list[0].get('email') if project.manager_list else None
            head_image_url = company.logo
            project_id = project.id

    # 获取辅导时长
    hour = int(project_interview.times) / 60 if project_interview.times else 0
    if isinstance(hour, float):
        hour = int(hour) if hour.is_integer() else hour
    # 处理辅导地点逻辑
    place = project_interview.place
    if not place or place == '1':
        if project_interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
            place = '线上'
        elif project_interview.place_type == ProjectInterviewPlaceTypeEnum.offline.value:
            place = '线下'
        else:
            place = '未知'
    # 构建并返回包含辅导详细信息的字典
    data = {
        "type": interview_type,
        "pay_type": pay_type,
        "user_count": user_count,
        "interview_time": f"{public_attr.start_time.strftime('%Y.%m.%d %H:%M')} - {public_attr.end_time.strftime('%H:%M')}",
        "start_time": public_attr.start_time.strftime("%Y-%m-%d %H:%M:%S"),
        "end_time": public_attr.end_time.strftime("%Y-%m-%d %H:%M:%S"),
        "topic": project_interview.message_topic,
        "customer_name": customer_name,
        "customer_email": customer_email,
        "head_image_url": head_image_url,
        "is_update": False,  # 不允许修改
        "is_platform_interview": True,  # 是平台内辅导
        "customer_id": customer_id,  # 跳转客户详情页
        "project_id": project_id,  # 跳转项目详情页
        "hour": hour,  # 辅导时长
        "interview_type_describe": interview_public.get_interview_type_describe(project_interview),  # 辅导类型
        "place_type": project_interview.place_type,  # 辅导地址类型
        "place": place,  # 辅导地址
        "interview_subject": project_interview.interview_subject,  # 教练形式
        "id": str(project_interview.id),  # 数据唯一标识
        "data_type": NonPlatformInterviewSourceEnum.interview.value,  # 数据类型，用于区分不同的辅导信息
    }
    return data


def public_courses_coach_to_dict(public_courses_coach):
    """
    将公开课信息对象转换为字典格式

    参数:
    - public_courses_coach: PublicCoursesCoach对象，代表一个公开课的信息。

    返回值:
    - data: 字典类型，包含了转换后的公开课信息。

    处理流程:
    1. 根据公开课的类型（一对一、小组辅导等），确定辅导类型的枚举值。
    2. 构建一个包含公开课详细信息的字典，包括辅导类型、支付类型、用户数量、辅导时间、开始时间、结束时间、主题、客户名称、客户邮箱、头像URL、是否允许修改、是否平台辅导、以及唯一标识符。
    3. 返回包含公开课信息的字典。
    """
    # 根据公开课的类型设置辅导类型
    if public_courses_coach.type == WorkTypeEnum.one_to_one.value:
        interview_type = NonPlatformInterviewTypeEnum.personal.value
    elif public_courses_coach.type in [WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value]:
        interview_type = NonPlatformInterviewTypeEnum.group.value
    else:
        interview_type = None

    # 获取辅导时长
    start_time = utc_date_time.datetime_change_utc(public_courses_coach.start_time)
    end_time = utc_date_time.datetime_change_utc(public_courses_coach.end_time)
    hour = round((end_time - start_time).total_hours(), 1)
    if isinstance(hour, float):
        hour = int(hour) if hour.is_integer() else hour

    # 构造返回的字典数据
    data = {
        "type": interview_type,  # 辅导类型
        "pay_type": NonPlatformInterviewPayTypeEnum.pay.value,  # 支付类型，默认为付费
        "user_count": 1,  # 公开课固定一条数据一个用户
        "interview_time": f"{public_courses_coach.start_time.strftime('%Y.%m.%d %H:%M')} - {public_courses_coach.end_time.strftime('%H:%M')}",  # 辅导时间段
        "start_time": public_courses_coach.start_time.strftime("%Y-%m-%d %H:%M:%S"),  # 开始时间
        "end_time": public_courses_coach.end_time.strftime("%Y-%m-%d %H:%M:%S"),  # 结束时间
        "topic": "公开课",  # 公开课默认主题
        "customer_name": public_courses_coach.student_name,  # 客户名称
        "customer_email": None,  # 客户邮箱
        "head_image_url": None,  # 头像URL
        "is_update": False,  # 是否允许修改，公开课信息不允许修改
        "is_platform_interview": False,  # 是否平台内辅导，这里设置为否
        "hour": hour,  # 辅导时长
        "id": str(public_courses_coach.id),  # 唯一标识符
        "data_type": NonPlatformInterviewSourceEnum.public_courses_coach.value  # 数据类型，用于区分不同的辅导信息
    }
    return data


def get_coach_interview_customer_data(interview_queryset, customer_type):
    """
    根据客户类型（全部、个人客户或项目客户）筛选出对应的辅导信息，并构造包含客户名称和公司简称的数据列表。
    此函数处理两种场景：一对一在线辅导（个人客户）和团队辅导（项目客户），并根据辅导类型筛选并汇总客户数据。

    :param interview_queryset: 辅导信息的查询集合
    :param customer_type: 客户类型（Null-全部, 1-个人客户, 2-项目客户）
    :return: 包含客户名称和公司简称的数据列表
    """
    data = []
    # 对于个人客户和全部客户，筛选在线一对一辅导的情况
    if not customer_type or int(customer_type) == 1:  # 全部, 1-个人客户
        user_interview_queryset = interview_queryset.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value
        )
        # 获取去重后的个人客户名称和相关公司简称
        distinct_customer_names_and_company = list(user_interview_queryset.values_list(
            'public_attr__target_user__true_name', 'public_attr__target_user__head_image_url', 'public_attr__project__company__short'
        ).distinct())
        for names_and_company_item in distinct_customer_names_and_company:
            customer_name, head_image_url, company_name = names_and_company_item
            data.append({
                "customer_name": customer_name,
                "company_name": company_name,
                "head_image_url": head_image_url
            })

    # 对于项目客户和全部客户，筛选团队辅导的情况
    if not customer_type or int(customer_type) == 2:  # 全部, 2-项目客户
        project_interview_queryset = interview_queryset.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value
        )
        # 获取去重后的项目名称和相关公司简称
        distinct_customer_projects_and_company = list(project_interview_queryset.values_list(
            'public_attr__project__name', 'public_attr__project__company__logo', 'public_attr__project__company__short'
        ).distinct())
        for projects_and_company_item in distinct_customer_projects_and_company:
            project_name, head_image_url, company_name = projects_and_company_item
            data.append({
                "customer_name": project_name,
                "company_name": company_name,
                "head_image_url": head_image_url
            })

    return data


def get_coach_interview_hour(coach_interview_queryset=None, project_interview_queryset=None,
                             public_courses_coach_queryset=None):
    """
    计算教练的辅导总时间，包括自主辅导、项目辅导和公开课辅导的时间。

    参数:
    - coach_interview_queryset: QuerySet，教练自主创建的辅导集合。
    - project_interview_queryset: QuerySet，项目辅导集合。
    - public_courses_coach_queryset: QuerySet，公开课辅导集合。

    返回值:
    - 返回一个字典，包含以下键值：
        - 'all_hour': 所有辅导类型的总时间。
        - 'coach_interview_hour': 教练自主创建的辅导总时间。
        - 'project_interview_hour': 项目辅导总时间。
        - 'public_courses_coach_hour': 公开课辅导总时间。

    详细说明:
    - 计算每个辅导类型的总时间，并将它们累加到'all_hour'中。
    - 'coach_interview_hour'通过聚合查询计算得到。
    - 'project_interview_hour'通过将辅导次数转换为小时计算得到。
    - 'public_courses_coach_hour'通过迭代每个公开课辅导记录，计算开始时间和结束时间之间的总小时数来得到。
    - 如果'all_hour'是浮点数但其值为整数时，会将其转换为整数类型。
    """
    all_hour = 0
    coach_interview_hour = 0
    project_interview_hour = 0
    public_courses_coach_hour = 0

    if coach_interview_queryset:
        # 从教练自主创建的辅导集合中聚合计算总小时数
        coach_interview_hour = coach_interview_queryset.aggregate(
            coach_interview_hour=Sum('hour'))['coach_interview_hour']
        all_hour += coach_interview_hour

    if project_interview_queryset:
        # 从项目辅导集合中聚合计算总次数，然后转换为小时
        project_interview_times = project_interview_queryset.aggregate(
            project_interview_times=Sum('times'))['project_interview_times']
        project_interview_hour = project_interview_times / 60 if project_interview_times else 0
        all_hour += project_interview_hour

    if public_courses_coach_queryset:
        # 遍历公开课辅导集合，计算每个辅导的时长并累加到总小时数
        public_courses_coach_hour = 0
        for item in public_courses_coach_queryset.all():
            # 计算每个公开课辅导的时长
            start_time = utc_date_time.datetime_change_utc(item.start_time)
            end_time = utc_date_time.datetime_change_utc(item.end_time)
            public_courses_coach_hour += round((end_time - start_time).total_hours(), 1)
        all_hour += public_courses_coach_hour

    # 只保留一位小数
    all_hour = round(all_hour, 1)
    # 如果总小时数是浮点数但实际为整数，转换为整数类型
    if isinstance(all_hour, float):
        all_hour = int(all_hour) if all_hour.is_integer() else all_hour

    data = {
        'all_hour': all_hour,
        'coach_interview_hour': coach_interview_hour,
        'project_interview_hour': project_interview_hour,
        'public_courses_coach_hour': public_courses_coach_hour,
    }
    return data


def get_coach_interview_customer(coach_user_id, customer_type):
    """
    根据教练的用户ID和客户类型，获取符合条件的辅导访谈客户信息。

    :param coach_user_id: 教练的用户ID
    :param customer_type: 客户类型 1-全部 2-一对一 3-项目  （2和3只查询平台内的客户信息）
    :return: 符合条件的客户数据列表
    """

    # 初始化查询集合，筛选出符合条件的辅导信息
    project_interview_queryset = ProjectInterview.objects.filter(
        is_coach_agree=True,  # 教练已同意
        deleted=False,  # 未被删除
        type=ProjectInterviewTypeEnum.formal_interview.value,  # 访谈类型为正式访谈
        public_attr__user_id=coach_user_id,  # 教练用户ID匹配
        public_attr__end_time__lt=datetime.datetime.now()  # 结束时间早于当前时间
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)  # 排除访谈取消的情况

    # 根据客户类型获取客户数据
    data = get_coach_interview_customer_data(project_interview_queryset, customer_type)

    if not customer_type:
        # 创建一个列表用于去重，保留已有的customer_name
        unique_data = [d['customer_name'] for d in data]

        # 获取教练自己录入的客户信息
        coach_interview_queryset = NonPlatformInterview.objects.filter(coach_user_id=coach_user_id, deleted=False)
        distinct_coach_interview_names = coach_interview_queryset.values_list('customer_name', flat=True).distinct()

        # 获取公开课的客户信息
        public_courses_coach_queryset = PublicCoursesCoach.objects.filter(
            coach__user_id=coach_user_id, deleted=False, type__in=[
                WorkTypeEnum.one_to_one.value, WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value])
        distinct_public_courses_coach_names = public_courses_coach_queryset.values_list(
            'student_name', flat=True).distinct()

        # 将教练自己录入的客户和公开课的学生名称添加到客户数据列表中（如果它们还不在列表中）
        for item in [*distinct_coach_interview_names, *distinct_public_courses_coach_names]:
            if item and item not in unique_data:
                data.append({
                    "customer_name": item,
                    "company_name": None,  # 公开课和教练自己录入的客户信息中可能没有企业名称
                    "head_image_url": None  # 公开课和教练自己录入的客户信息中可能没有头像URL
                })
                unique_data.append(item)

    return data


def get_coach_interview_list(params):
    """
    根据传入的参数获取教练的辅导信息列表，包括教练自创建的辅导、平台辅导信息以及公开课信息。

    参数:
    - params: 字典类型，包含用于筛选辅导信息的各种条件，如开始日期、结束日期、支付类型、面试类型、客户名称、教练用户ID等。

    返回值:
    - 返回一个元组，包含三个查询集：
        1. coach_interview_queryset: 教练创建的辅导信息查询集。
        2. project_interview_queryset: 平台辅导信息查询集。
        3. public_courses_coach_queryset: 公开课信息查询集，如果指定的支付类型为免费，则不包括此查询集。

    处理流程:
    1. 根据教练用户ID和其他筛选条件过滤教练创建的辅导信息。
    2. 根据相同的筛选条件过滤平台的辅导信息，额外考虑辅导状态和是否已同意辅导的条件。
    3. 如果支付类型不是仅免费，还将根据筛选条件过滤公开课信息。
    4. 根据面试类型细分筛选条件，以适应不同的辅导场景（一对一、小组辅导等）。
    5. 返回筛选后的辅导信息查询集合。
    """
    # 从参数中获取各种过滤条件
    start_date = params.get('start_date')
    end_date = params.get('end_date')
    pay_type = params.get('pay_type')
    interview_type = params.get('type')
    customer_name = params.get('customer_name')
    coach_user_id = params.get('coach_user_id')

    # 获取教练创建的辅导信息，确保这些信息未被删除
    coach_interview_queryset = NonPlatformInterview.objects.filter(coach_user_id=coach_user_id, deleted=False)

    # 根据不同条件对教练创建的辅导信息进行过滤的逻辑
    if start_date:
        coach_interview_queryset = coach_interview_queryset.filter(start_time__date__gte=start_date)
    if end_date:
        coach_interview_queryset = coach_interview_queryset.filter(end_time__date__lte=end_date)
    if pay_type:
        coach_interview_queryset = coach_interview_queryset.filter(pay_type=pay_type)
    if interview_type:
        coach_interview_queryset = coach_interview_queryset.filter(type=interview_type)
    if customer_name:
        coach_interview_queryset = coach_interview_queryset.filter(customer_name__contains=customer_name)

    # 获取平台的辅导信息，确保教练已同意，信息未被删除，且辅导为正式辅导
    project_interview_queryset = ProjectInterview.objects.filter(
        is_coach_agree=True, deleted=False,
        type=ProjectInterviewTypeEnum.formal_interview.value,
        public_attr__user_id=coach_user_id,
        public_attr__end_time__lt=datetime.datetime.now()
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)  # 排除已取消的辅导

    # 以下是根据不同条件对平台的辅导信息进行过滤的逻辑
    if start_date:
        project_interview_queryset = project_interview_queryset.filter(public_attr__start_time__date__gte=start_date)
    if end_date:
        project_interview_queryset = project_interview_queryset.filter(public_attr__end_time__date__lte=end_date)
    if pay_type:
        if int(pay_type) == NonPlatformInterviewPayTypeEnum.pay.value:
            project_interview_queryset = project_interview_queryset.exclude(order__payer_amount=0)  # 排除免费的辅导
        elif int(pay_type) == NonPlatformInterviewPayTypeEnum.free.value:
            project_interview_queryset = project_interview_queryset.filter(order__payer_amount=0)  # 只包括免费的辅导
    if customer_name:
        project_interview_queryset = project_interview_queryset.filter(
            Q(public_attr__target_user__true_name=customer_name) |
            Q(public_attr__project__name=customer_name)
        )
    if interview_type:
        if int(interview_type) == NonPlatformInterviewTypeEnum.personal.value:
            project_interview_queryset = project_interview_queryset.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value
            )
        elif int(interview_type) == NonPlatformInterviewTypeEnum.group.value:
            project_interview_queryset = project_interview_queryset.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value
            )

    # 如果没有支付类型，或者选择已支付类型，需要获取公开课信息
    if not pay_type or int(pay_type) == NonPlatformInterviewPayTypeEnum.pay.value:
        public_courses_coach_queryset = PublicCoursesCoach.objects.filter(
            coach__user_id=coach_user_id, deleted=False)
        if start_date:
            public_courses_coach_queryset = public_courses_coach_queryset.filter(
                start_time__date__gte=start_date)
        if end_date:
            public_courses_coach_queryset = public_courses_coach_queryset.filter(
                end_time__date__lte=end_date)

        if customer_name:
            public_courses_coach_queryset = public_courses_coach_queryset.filter(
                student_name=customer_name
            )

        # 默认只查询辅导有关的三种类型
        work_type = [WorkTypeEnum.one_to_one.value, WorkTypeEnum.group_coach.value,
                     WorkTypeEnum.group_counseling.value]
        if interview_type:
            if int(interview_type) == NonPlatformInterviewTypeEnum.personal.value:
                work_type = [WorkTypeEnum.one_to_one.value]
            elif int(interview_type) == NonPlatformInterviewTypeEnum.group.value:
                work_type = [WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value]
        public_courses_coach_queryset = public_courses_coach_queryset.filter(
            type__in=work_type
        )

    else:
        public_courses_coach_queryset = None

    return coach_interview_queryset, project_interview_queryset,public_courses_coach_queryset


def coach_interview_data_save_excel(coach_interview_queryset, project_interview_queryset, public_courses_coach_queryset, user):
    """
    将教练辅导访谈数据保存为Excel文件，并上传到阿里云。

    :param coach_interview_queryset: 教练创建辅导的查询集
    :param project_interview_queryset: 平台内辅导的查询集
    :param public_courses_coach_queryset: 公开课辅导的查询集
    :param user: 当前操作的用户对象
    :return: 上传到阿里云的文件URL
    """

    # Excel表格标题行
    title = ["客户名称", "客户邮箱", "辅导类型", "辅导人数", "开始日期", "结束日期", "付费辅导小时数", "免费辅导小时数"]
    data = [title]
    raw_data = {}

    # 教练名称里面如果有 “/” 在阿里云文件会出现切分，需要转化
    coach_name = user.true_name
    coach_name = str(coach_name).replace('/', '_')

    # 教练创建辅导数据
    coach_interview_data = []
    if coach_interview_queryset:
        coach_interview_data = AppCoachInterviewDataSerializer(coach_interview_queryset, many=True).data
    # 平台内辅导数据
    all_project_interview_data = []
    if project_interview_queryset:
        for item in project_interview_queryset.all():
            project_interview_data = coach_project_interview_to_dict(item)
            all_project_interview_data.append(project_interview_data)

    # 公开课辅导数据
    all_public_courses_coach_data = []
    # 获取公开课数据
    if public_courses_coach_queryset:
        for item in public_courses_coach_queryset.all():
            public_courses_coach_data = public_courses_coach_to_dict(item)
            all_public_courses_coach_data.append(public_courses_coach_data)

    # 数据拼接
    all_data = [*coach_interview_data, *all_project_interview_data, *all_public_courses_coach_data]
    # 数据排序
    sorted_data = sorted(all_data, key=lambda x: x['start_time'], reverse=True)

    msg_str = "%$%"  # 自定义名称可能出现/，_等特殊字符，用来分隔数据的占位符需要“独特”
    for item in sorted_data:
        # 构建唯一键
        key = f'{item["customer_name"]}{msg_str}{item["customer_email"]}{msg_str}{item["type"]}'
        if key not in raw_data.keys():
            raw_data[key] = {
                'pay_hour': 0,
                'free_hour': 0,
                'user_count': item.get('user_count'),
                'start_time': pendulum.parse(item["start_time"]),
                'end_time': pendulum.parse(item["end_time"])
            }
            # 根据支付类型 增加不同时长
            if item.get('pay_type') == NonPlatformInterviewPayTypeEnum.pay.value:
                raw_data[key]['pay_hour'] = item.get('hour')
            elif item.get('pay_type') == NonPlatformInterviewPayTypeEnum.free.value:
                raw_data[key]['free_hour'] = item.get('hour')

        else:
            raw_data[key]['user_count'] += item.get('user_count')
            # 更新开始时间和结束时间
            start_time = pendulum.parse(item["start_time"])
            end_time = pendulum.parse(item["end_time"])
            if start_time < raw_data[key]["start_time"]:
                raw_data[key]["start_time"] = start_time
            if end_time > raw_data[key]["end_time"]:
                raw_data[key]["end_time"] = end_time

            if item.get('pay_type') == NonPlatformInterviewPayTypeEnum.pay.value:
                raw_data[key]['pay_hour'] += item.get('hour')
            elif item.get('pay_type') == NonPlatformInterviewPayTypeEnum.free.value:
                raw_data[key]['free_hour'] += item.get('hour')

    # 数据组合
    for k, v in raw_data.items():
        customer_name, customer_email, interview_type = k.split(msg_str)

        if customer_email == "None":
            customer_email = '--'
        user_count = v.get('user_count')
        start_time = v.get('start_time')
        end_time = v.get('end_time')
        pay_hour = v.get('pay_hour')
        free_hour = v.get('free_hour')
        data.append([
            customer_name,
            customer_email,
            NonPlatformInterviewTypeEnum.get_display(int(interview_type)),
            user_count,
            start_time.strftime('%Y-%m-%d'),
            end_time.strftime('%Y-%m-%d'),
            pay_hour if pay_hour else 0,
            free_hour if free_hour else 0
        ])

    # 拼接阿里云文件路径
    current_time = pendulum.now()
    formatted_time = current_time.strftime('%Y_%m_%d_%H_%M_%S')
    microseconds = current_time.microsecond
    file_base_name = f"ICF 教练辅导日志-{coach_name}.xlsx"
    name = f'app/coach_interview/{formatted_time}_{microseconds}/{file_base_name}'

    # 获取文件数据
    file = excel_pubilc.save_excel(data, 'coach_interview', "ICF 教练辅导日志")
    # 上传阿里云
    aliyun.AliYun('cwcoach').send_file(name, file.getvalue())

    return file,  file_base_name, f'{settings.ALIYUN_SDN_BASE_URL}/{name}'
