from utils.task import shared_task
from wisdom_v2.models import ProjectInterview, GrowthGoals
from wisdom_v2.models_file import GrowthGoalsModule
from utils.messagecenter.center import push_v2_message


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_interview_tips(project_interview_id):
    """
    如果项目配置了成长目标，且当次辅导后填写，就发送特殊版本约谈提醒邮件
    :param project_interview_id: 约谈id
    :return: bool 是否发送成长目标提醒
    """
    try:
        # 获取约谈信息
        project_interview = ProjectInterview.objects.filter(
            id=project_interview_id,
            deleted=False
        ).first()

        if not project_interview:
            return False

        # 获取项目和成员信息
        project = project_interview.public_attr.project
        target_user = project_interview.public_attr.target_user  # 被教练者
        coach_user = project_interview.public_attr.user  # 教练

        # 先检查项目是否配置了成长目标
        has_growth_goals_config = GrowthGoalsModule.objects.filter(
            project_bundle__project=project,
            deleted=False
        ).exists()

        if not has_growth_goals_config:
            return False

        # 再查看本次辅导后是否有成长目标填写
        # 检查在约谈时间之后是否有成长目标被创建或更新
        interview_start_time = project_interview.public_attr.start_time

        # 查找在约谈开始时间之后创建的成长目标
        recent_growth_goals = GrowthGoals.objects.filter(
            public_attr__project=project,
            public_attr__target_user=target_user,
            created_at__gte=interview_start_time,
            deleted=False
        ).exists()

        # 或者查找在约谈开始时间之后更新的成长目标
        updated_growth_goals = GrowthGoals.objects.filter(
            public_attr__project=project,
            public_attr__target_user=target_user,
            updated_at__gte=interview_start_time,
            deleted=False
        ).exists()

        has_growth_goals_filled = recent_growth_goals or updated_growth_goals

        if not has_growth_goals_filled:
            return False

        # 有就发送邮件 - 发送特殊版本的约谈提醒邮件
        interview_time = project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + \
                        '-' + project_interview.public_attr.end_time.strftime('%H:%M')

        # 发送给教练的成长目标提醒邮件
        push_v2_message.delay(
            coach_user,
            'interview_add_growth_goals',
            param={
                'interview_time': interview_time,
                'target_user': target_user.cover_name,
                'project_name': project.full_name
            },
            project_id=project.id
        )

        # 发送给被教练者的成长目标提醒邮件
        push_v2_message.delay(
            target_user,
            'interview_add_growth_goals',
            param={
                'interview_time': interview_time,
                'target_user': coach_user.cover_name,
                'project_name': project.full_name
            },
            project_id=project.id
        )

        return True

    except Exception as e:
        # 记录错误但不影响主流程
        import logging
        logger = logging.getLogger('api_action')
        logger.error(f'send_interview_tips error: {e}, project_interview_id: {project_interview_id}')
        return False