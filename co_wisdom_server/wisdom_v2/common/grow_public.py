from utils.task import shared_task
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum
from wisdom_v2.models import ProjectInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_interview_tips(project_interview_id):
    """
    如果项目配置了成长目标，且当次辅导后填写，就发送特殊版本约谈提醒邮件
    :param project_interview_id: 约谈id
    :return: bool 是否发送成长目标提醒
    """
    # 先检查项目是否配置了成长目标
    # 再查看本次辅导后是否有成长目标填写
    # 有就发送邮件
    

    