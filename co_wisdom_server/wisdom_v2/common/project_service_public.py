import copy
import logging
import math
import pickle
import uuid
import redis

from django.conf import settings
from django.db.models import Sum, F, Q, Value
from django.db.models.functions import Coalesce

from utils import task
from utils.api_response import WisdomValidationError
from utils.messagecenter import center
from utils.send_account_email import get_project_manage_qr_code

from wisdom_v2.common import stakeholder_interview_public, interview_public, one_to_one_coach_public, coach_task_public
from wisdom_v2.common import group_coach_public
from wisdom_v2.common.project_coach_public import get_project_coach_exists, get_mop_project_coach_exists
from wisdom_v2.common.change_observation_public import get_change_observation_title
from wisdom_v2.common import project_member_public
from wisdom_v2.common.stakeholder_interview_public import check_elements_exist
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum
from wisdom_v2.enum.project_interview_enum import DataType, ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum, \
    GroupCoachTypeEnum, InterviewRecordTypeE<PERSON>, InterviewSub<PERSON>Enum
from wisdom_v2.enum.service_content_enum import (GrowthGoalsModelTypeEnum, NewCoachTaskTypeEnum, \
                                                 InterviewRecordTemplateRoleEnum, PersonalReportTypeEnum, DataTypeEnum,
                                                 CoachTypeEnum, EvaluationWriteRoleEnum,
                                                 MultipleAssociationRelationTypeEnum, ChangeObservationInviteTypeEnum)
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models import EvaluationModule, EvaluationReport, ProjectInterview, OneToOneCoach, ArticleModule, \
    CoachTask, ChangeObservation, PersonalReport, GroupCoach, DataViewPermission, ProjectCoach, Article, Evaluation, \
    TotalTemplate, InterviewRecordTemplate, Coach, ProjectMember, ProjectGroupCoach, PublicAttr, \
    GrowthGoals2ChangeObservation, MultipleAssociationRelation, ProjectInterested, ProjectBundle, UserTmp, \
    ChangeObservationAnswer, Project, InterviewRecordTemplateAnswer

from wisdom_v2.models_file import ProjectMemberServiceContent, ProjectServiceStage, GrowthGoalsModule, \
    ChemicalInterviewModule, ChemicalInterview2Coach, StakeholderInterview, StakeholderInterviewModule, \
    ProjectServiceContent, ProjectServiceMember
from datetime import datetime, timedelta

from wisdom_v2.models_file.project_service import ServiceStage
from wisdom_v2.views.change_observation_action import ChangeObservationDetailSerializer
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CANCEL, MANAGE_EVALUATION, \
    ATTR_TYPE_EVALUATION_ANSWER, LBI_EVALUATION, CUSTOMIZED_PROJECT_IDS
from wisdom_v2.views.chemical_interview_action import ChemicalInterviewModuleSerializer, get_random_coach
from wisdom_v2.views.stakeholder_interview_action import StakeholderInterviewModuleSerializer

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


def update_project_member_service_content(model_content, service_model):
    """
    根据项目的服务配置，提前创建基础数据，并更新项目服务内容中的content
    :param model_content: 项目服务内容数据
    :param service_model: 服务模型
    :return: model_content 更新后的数据
    """

    # 如果是工作坊，提前创建project_group_coach_id并将id保存到content中
    if service_model.content_type == DataType.group_coach.value:
        for item in model_content:
            if not item.get('id'):
                project_group_coach, status = ProjectGroupCoach.objects.get_or_create(
                    project_id=service_model.project_id,
                    type=GroupCoachTypeEnum.collective_tutoring.value,
                    start_time=item['course_date'] + ' ' + item['start_course_time'] + ':00',
                    end_time=item['course_date'] + ' ' + item['end_course_time'] + ':00',
                    place=item['course_place'],
                    theme=item['theme'],
                    deleted=False)
                item['project_group_coach_id'] = project_group_coach.id

    # 如果是小组辅导，提前创建project_group_coach_id并将id保存到content中
    elif service_model.content_type == DataType.group_tutoring.value:
        for item in model_content:
            if not item.get('id'):
                project_group_coach, status = ProjectGroupCoach.objects.get_or_create(
                    project_id=service_model.project_id,
                    type=GroupCoachTypeEnum.group_tutoring.value,
                    start_time=item['course_date'] + ' ' + item['start_course_time'] + ':00',
                    end_time=item['course_date'] + ' ' + item['end_course_time'] + ':00',
                    place=item['course_place'],
                    theme=item['theme'],
                    deleted=False)
                item['project_group_coach_id'] = project_group_coach.id
    return model_content


def is_member_update_content_unlink_existing(service_model, content_id, content):
    """
    判断项目成员更新服务内容是否需要解除关联
    默认解除关联，修改指定类型的特殊字段不解除
    return: True 解除关联，False 不解除关联
    """
    is_unlink_existing = True
    if service_model.content_type == DataType.chemical_interview.value:

        chemical_interview_model = ChemicalInterviewModule.objects.filter(id=content_id, deleted=False).first()
        if all([
            chemical_interview_model.max_interview_number == content.get('max_interview_number'),
            chemical_interview_model.duration == content.get('duration'),
            chemical_interview_model.start_time.strftime('%Y-%m-%d') == content.get('start_date'),
            chemical_interview_model.end_time.strftime('%Y-%m-%d') == content.get('end_date'),
            chemical_interview_model.coach_source == content.get('coach_source'),
        ]):
            is_unlink_existing = False
    elif service_model.content_type == DataType.stakeholder_interview.value:
        stakeholder_interview_model = StakeholderInterviewModule.objects.filter(id=content_id, deleted=False).first()
        if all([
            stakeholder_interview_model.stakeholder_interview_number == content.get('stakeholder_interview_number'),
            stakeholder_interview_model.duration == content.get('duration'),
            stakeholder_interview_model.start_date.strftime('%Y-%m-%d') == content.get('start_date'),
            stakeholder_interview_model.end_date.strftime('%Y-%m-%d') == content.get('end_date'),
            stakeholder_interview_model.coach_template_id == content.get('coach_template_id'),
            stakeholder_interview_model.report_template_id == content.get('report_template_id'),
        ]):
            is_unlink_existing = False
    elif service_model.content_type == DataType.change_observation.value:
        change_observation = ChangeObservation.objects.filter(id=content_id, deleted=False).first()

        if change_observation.stakeholders_write_end_date:
            stakeholders_write_end_date = change_observation.stakeholders_write_end_date.strftime('%Y-%m-%d')
        else:
            stakeholders_write_end_date = None

        if change_observation.invite_end_time:
            invite_end_time = change_observation.invite_end_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            invite_end_time = None
        if all([
            change_observation.write_condition == content.get('write_condition'),
            change_observation.remind_type == content.get('remind_type'),
            invite_end_time == content.get('invite_end_time'),
            stakeholders_write_end_date == content.get('stakeholders_write_end_date'),
            change_observation.invite_type == content.get('invite_type')
        ]):
            is_unlink_existing = False


    elif service_model.content_type == DataType.coach_tasks.value:
        coach_task = CoachTask.objects.filter(id=content_id, deleted=False).first()
        if all([
            coach_task.template_id == content.get('template_id'),
            coach_task.hours == content.get('hours')
        ]):
            is_unlink_existing = False
    return is_unlink_existing


def cp_project_member_service_content(cp_project_member_content, target_project_member):
    """
    复制项目成员服务内容
    :param cp_project_member_content: 被复制的项目成员服务内容
    :param target_project_member: 目标项目成员
    """

    target_project_bundle = ProjectBundle.objects.filter(project_member=target_project_member, deleted=False).first()
    if not target_project_bundle:
        target_project_bundle = ProjectBundle.objects.create(project=target_project_member.project,
                                                             project_member=target_project_member)

    object_ids = []
    if cp_project_member_content.content_type == DataType.article.value:

        article = ArticleModule.objects.filter(id__in=cp_project_member_content.object_ids, deleted=False).all()

        for item in article:
            # 文章模块
            article_model = ArticleModule.objects.create(**{
                "article_id": item.article_id, "end_time": item.end_time,
                "project_bundle_id": target_project_bundle.id, 'start_time': item.start_time})
            object_ids.append(article_model.id)

    elif cp_project_member_content.content_type == DataType.evaluation.value:

        evaluation_module = EvaluationModule.objects.filter(
            id__in=cp_project_member_content.object_ids, deleted=False).all()

        for evaluation in evaluation_module:

            data_dict = {"evaluation_id": evaluation.evaluation_id,
                         "start_time": evaluation.start_time,
                         "end_time": evaluation.end_time,
                         "project_bundle_id": target_project_bundle.id,
                         "is_open_screen": evaluation.is_open_screen}

            new_evaluation = EvaluationModule.objects.create(**data_dict)
            if evaluation.evaluation.role == EvaluationWriteRoleEnum.coachee_stakeholder.value:
                center.send_evaluation_message.apply_async(kwargs=dict(
                    project_member_id=target_project_member.pk, evaluation_module_id=evaluation.id),
                    countdown=3, expires=120)
            view_permission = DataViewPermission.objects.filter(data_id=evaluation.id,
                                                                type=DataTypeEnum.evaluation,
                                                                deleted=False).first()

            DataViewPermission.objects.create(type=DataTypeEnum.evaluation.value, data_id=new_evaluation.id,
                                              role=view_permission.role)
            object_ids.append(new_evaluation.id)

    elif cp_project_member_content.content_type == DataType.coach_tasks.value:
        # 教练任务模块
        project_coach = ProjectCoach.objects.filter(
            project_id=target_project_member.project_id,
            member_id=target_project_member.user_id,
            project_group_coach__isnull=True,
            coach__isnull=False,
            deleted=False).first()
        coach_tasks = CoachTask.objects.filter(id__in=cp_project_member_content.object_ids, deleted=False).all()
        if project_coach:
            coach_id = project_coach.coach.user_id
        else:
            coach_id = None
        for coach_task in coach_tasks:
            coach_task = project_member_public.add_coach_task(coach_id, coach_task.template.id, coach_task.hours, target_project_member,
                                        target_project_bundle)
            object_ids.append(coach_task.id)

    elif cp_project_member_content.content_type == DataType.chemical_interview.value:

        chemical_interview_module = ChemicalInterviewModule.objects.filter(id__in=cp_project_member_content.object_ids,
                                                                           deleted=False).all()

        for exists_chemical_interview_module in chemical_interview_module:
            new_chemical_interview_module = ChemicalInterviewModule.objects.create(
                project_member=target_project_member,
                max_interview_number=exists_chemical_interview_module.max_interview_number,
                duration=exists_chemical_interview_module.duration,
                start_time=exists_chemical_interview_module.start_time,
                end_time=exists_chemical_interview_module.end_time,
                coach_source=exists_chemical_interview_module.coach_source
            )
            if new_chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                coaches = get_random_coach(target_project_member.pk,
                                           target_project_member.project_id,
                                           new_chemical_interview_module.max_interview_number)
                for coach in coaches:
                    ChemicalInterview2Coach.objects.create(
                        coach=coach, chemical_interview_module=new_chemical_interview_module,
                        chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback
                    )
            object_ids.append(new_chemical_interview_module.id)

    elif cp_project_member_content.content_type == DataType.stakeholder_interview.value:
        stakeholder_interview_module = StakeholderInterviewModule.objects.filter(
            id__in=cp_project_member_content.object_ids, deleted=False).all()

        for exists_stakeholder_interview_module in stakeholder_interview_module:
            # 利益相关者访谈模块
            exists_stakeholder_interview_module = exists_stakeholder_interview_module
            project_coach = ProjectCoach.objects.filter(
                project_id=target_project_member.project_id,
                member_id=target_project_member.user_id,
                project_group_coach__isnull=True,
                coach__isnull=False,
                deleted=False).first()
            if project_coach:
                coach_id = project_coach.coach.user_id
            else:
                coach_id = None
            coach_task = project_member_public.add_coach_task(coach_id, exists_stakeholder_interview_module.report_template_id, 0,
                                        target_project_member, target_project_bundle)
            module = StakeholderInterviewModule.objects.create(
                project_member=target_project_member,
                stakeholder_interview_number=exists_stakeholder_interview_module.stakeholder_interview_number,
                duration=exists_stakeholder_interview_module.duration,
                start_date=exists_stakeholder_interview_module.start_date,
                end_date=exists_stakeholder_interview_module.end_date,
                coach_template=exists_stakeholder_interview_module.coach_template,
                report_template=exists_stakeholder_interview_module.report_template,
                coach_task=coach_task
            )
            object_ids.append(module.id)

    elif cp_project_member_content.content_type == DataType.interview.value:

        one_to_one_coach_all = OneToOneCoach.objects.filter(id__in=cp_project_member_content.object_ids,
                                                            deleted=False).all()

        for one_to_one_coach in one_to_one_coach_all:
            data_dict = {
                "type": one_to_one_coach.type,
                "project_bundle_id": target_project_bundle.id,
                "online_available_time": one_to_one_coach.online_available_time,
                "suggested_interval": one_to_one_coach.suggested_interval,
                "suggested_duration": one_to_one_coach.suggested_duration,
                "suggested_start_date": one_to_one_coach.suggested_start_date,
                "online_available_coaching_duration": one_to_one_coach.online_available_coaching_duration,
                "online_office_coaching_ceiling": one_to_one_coach.online_office_coaching_ceiling,
                "online_coaching_ceiling": one_to_one_coach.online_coaching_ceiling,
                "online_time": one_to_one_coach.online_time,
                "coach_match_type": one_to_one_coach.coach_match_type,
            }
            obj = OneToOneCoach.objects.create(**data_dict)
            object_ids.append(obj.id)

    elif cp_project_member_content.content_type == DataType.growth_goals.value:

        growth_goals = GrowthGoalsModule.objects.filter(id__in=cp_project_member_content.object_ids,
                                                        deleted=False).all()

        for item in growth_goals:
            growth_goals_obj = GrowthGoalsModule.objects.create(
                type=item.type,
                hours=item.hours,
                start_date=item.start_date,
                end_date=item.end_date,
                project_bundle=target_project_bundle,
            )
            object_ids.append(growth_goals_obj.id)

    # 工作坊模块
    elif cp_project_member_content.content_type == DataType.group_coach.value:

        group_coach_list = GroupCoach.objects.filter(id__in=cp_project_member_content.object_ids, deleted=False).all()
        for group_coach in group_coach_list:

            power_tag = list(group_coach.power_tag.filter(deleted=False).values_list('tag', flat=True))
            content = {
                'start_course_time': group_coach.project_group_coach.start_time,
                'end_course_time': group_coach.project_group_coach.end_time,
                'course_place': group_coach.project_group_coach.place,
                'theme': group_coach.project_group_coach.theme,
                'template_id': group_coach.inter_view_record_template_id,
                'project_group_coach_id': group_coach.project_group_coach_id,
                'power_tag': power_tag,
            }
            obj = group_coach_public.create_group_coach_details(content, target_project_bundle, GroupCoachTypeEnum.collective_tutoring.value)
            object_ids.append(obj.id)

    # 小组辅导模块
    elif cp_project_member_content.content_type == DataType.group_tutoring.value:
        group_coach_list = GroupCoach.objects.filter(id__in=cp_project_member_content.object_ids, deleted=False).all()
        for group_coach in group_coach_list:
            power_tag = list(group_coach.power_tag.filter(deleted=False).values_list('tag', flat=True))
            content = {
                'start_course_time': group_coach.project_group_coach.start_time,
                'end_course_time': group_coach.project_group_coach.end_time,
                'course_place': group_coach.project_group_coach.place,
                'theme': group_coach.project_group_coach.theme,
                'template_id': group_coach.inter_view_record_template_id,
                'project_group_coach_id': group_coach.project_group_coach_id,
                'power_tag': power_tag,
            }
            obj = group_coach_public.create_group_coach_details(content, target_project_bundle, GroupCoachTypeEnum.group_tutoring.value)
            object_ids.append(obj.id)


    elif cp_project_member_content.content_type == DataType.change_observation.value:
        change_observation = ChangeObservation.objects.filter(id__in=cp_project_member_content.object_ids,
                                                              deleted=False).all()
        for item in change_observation:
            write_condition = item.write_condition
            remind_type = item.remind_type
            invite_end_time = item.invite_end_time
            stakeholders_write_end_date = item.stakeholders_write_end_date
            invite_type = item.invite_type
            max_stakeholders_count = item.max_stakeholders_count
            name = get_change_observation_title(target_project_member)
            instance = ChangeObservation.objects.create(
                remind_type=remind_type, name=name, project_member=target_project_member,
                write_condition=write_condition, invite_end_time=invite_end_time,
                stakeholders_write_end_date=stakeholders_write_end_date,
                invite_type=invite_type, max_stakeholders_count=max_stakeholders_count
            )
            object_ids.append(instance.id)

    return object_ids


def del_project_member_service(service_model, content_id):
    """
    删除项目成员的服务
    :param service_model: 服务模型
    :param content_id: 服务内容id
    :return:
    """
    if service_model.content_type == DataType.article.value:
        ArticleModule.objects.filter(id=content_id, deleted=False).update(deleted=True)

    elif service_model.content_type == DataType.coach_tasks.value:
        CoachTask.objects.filter(id=content_id).update(deleted=True)
    elif service_model.content_type == DataType.evaluation.value:
        evaluation_module = EvaluationModule.objects.filter(id=content_id, deleted=False).first()

        # 优先看完成时间
        if evaluation_module.submit_time:
            raise WisdomValidationError('该测评已有人完成，不支持移除')

        # lbi需要看是否有单个利益相关者已提交
        if evaluation_module.evaluation.code == LBI_EVALUATION:

            # user和target_user同时存在的是lbi测评。查询该项目下是否有满足条件的回答。
            if PublicAttr.objects.filter(
                    project=service_model.project_member.project,
                    user__isnull=False,
                    target_user=service_model.project_member.user,
                    type=ATTR_TYPE_EVALUATION_ANSWER).exists():
                raise WisdomValidationError('该测评已有人完成，不支持移除')

        DataViewPermission.objects.filter(
            data_id=evaluation_module.id,
            type=DataTypeEnum.evaluation.value, deleted=False).update(deleted=True)
        evaluation_module.deleted = True
        evaluation_module.save()

    elif service_model.content_type == DataType.growth_goals.value:
        GrowthGoalsModule.objects.filter(id=content_id, deleted=False).update(deleted=True)

    elif service_model.content_type == DataType.stakeholder_interview.value:

        instance = StakeholderInterviewModule.objects.filter(id=content_id, deleted=False).first()
        if instance.stakeholder_interview.filter(interview__isnull=False).exists():
            raise WisdomValidationError('当前参与人员已预约辅导，无法移除利益相关者访谈配置')
        if InterviewRecordTemplateAnswer.objects.filter(public_attr=instance.coach_task.public_attr,
                                                        deleted=False).exists():
            raise WisdomValidationError('当前教练已填写利益相关者访谈报告，无法移除利益相关者访谈配置')
        # 移除利益相关者访谈配置
        instance.stakeholder_interview.filter(deleted=False).update(deleted=True)
        instance.deleted = True
        instance.save()
        coach_task = instance.coach_task
        coach_task.deleted = True
        coach_task.save()
    elif service_model.content_type == DataType.group_coach.value:
        group_coach = GroupCoach.objects.filter(pk=content_id, deleted=False).first()
        if group_coach:
            group_coach_public.del_group_coach(group_coach)

    elif service_model.content_type == DataType.group_tutoring.value:
        group_coach = GroupCoach.objects.filter(pk=content_id, deleted=False).first()
        if group_coach:
            group_coach_public.del_group_coach(group_coach)

    elif service_model.content_type == DataType.interview.value:
        OneToOneCoach.objects.filter(pk=content_id, deleted=False).update(deleted=True)

    elif service_model.content_type == DataType.change_observation.value:
        instance = ChangeObservation.objects.filter(pk=content_id, deleted=False).first()
        is_write = ChangeObservationAnswer.objects.filter(change_observation=instance).exists()
        if is_write:
            raise WisdomValidationError('当前改变观察已填写，无法移除')
        instance.deleted = True
        instance.save()
        GrowthGoals2ChangeObservation.objects.filter(change_observation=instance).update(deleted=True)
        MultipleAssociationRelation.objects.filter(
            main_id=instance.pk,
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value).update(
            deleted=True)

    elif service_model.content_type == DataType.chemical_interview.value:
        instance = ChemicalInterviewModule.objects.filter(pk=content_id, deleted=False).first()
        if instance.coaches.filter(deleted=False, interview__isnull=False).exists():
            raise WisdomValidationError('用户已预约化学面谈，不可删除')
        instance.coaches.filter(deleted=False).update(deleted=True)
        instance.deleted = True
        instance.save()
    return


def create_project_member_service_content_all(service_content, project_member, project_service_stage=None):
    """
    创建项目成员服务内容
    :param service_content: 项目服务内容
    :param project_member: 项目成员
    :param project_service_stage: 项目服务阶段
    """

    for item in service_content:
        member_service = ProjectMemberServiceContent.objects.create(
            project_member=project_member,
            content_type=item.content_type,
            service_stage=project_service_stage,
        )
        if item.content:
            state = project_service_content_validate(item, item.content, project_member)
            if state:
                continue
            object_ids = project_member_service_content_create(item.content, member_service)
        else:
            object_ids = []
        member_service.object_ids = [str(i) for i in object_ids]
        member_service.save()
        ProjectServiceMember.objects.create(member_service=member_service, project_service=item)


def create_project_member_service_all(project_member):
    """
    项目用户复制项目服务配置
    :param project_member: 项目成员
    """

    project_service_stage = ProjectServiceStage.objects.filter(project=project_member.project, deleted=False).order_by(
        'order').all()

    if project_service_stage:
        for project_stage in project_service_stage:
            service_content = ProjectServiceContent.objects.filter(service_stage=project_stage, deleted=False).all()
            create_project_member_service_content_all(service_content, project_member,
                                                      project_service_stage=project_stage)
    else:
        service_content = ProjectServiceContent.objects.filter(project=project_member.project, deleted=False).all()
        create_project_member_service_content_all(service_content, project_member, project_service_stage=None)


def get_project_member_service_ids(stage_id, project_member_ids, model_type_ids):
    """
    获取服务阶段关联服务内容
    :param stage_id: 项目服务阶段id
    :param project_member_ids: 项目成员id
    :param model_type_ids: 项目服务内容类型id
    :return: 项目成员服务内容id
    """
    stage = ProjectServiceStage.objects.filter(id=stage_id, deleted=False).first()
    project_member_service_ids = []

    for item in project_member_ids:
        project_member = ProjectMember.objects.filter(id=item, deleted=False).first()
        if project_member:
            if project_member.project_service_stage.filter(deleted=False).exists():
                raise WisdomValidationError(f'{project_member.user.cover_name}用户已经自定义阶段,无法添加服务内容')

            # 如果添加内容的时候用户没有选择是否分阶段 根据内容适配是否分阶段
            if not ServiceStage.objects.filter(project_member=project_member, deleted=False).exists():
                is_stage = True if stage_id else False
                ServiceStage.objects.create(
                    project_member=project_member, is_stage=is_stage, deleted=False)

            stage_id = str(stage.id) if stage else None
            create_list = []
            for c_id in model_type_ids:
                create_list.append(ProjectMemberServiceContent(
                    project_member_id=item,
                    content_type=c_id,
                    service_stage_id=stage_id))
            project_member_service = ProjectMemberServiceContent.objects.bulk_create(create_list)
            project_member_service_ids.append([str(item.id) for item in project_member_service])
    return project_member_service_ids


def create_project_member_service_content(model_content, service_model):
    """
    创建项目成员服务内容
    :param model_content: 项目服务内容
    :param service_model: 项目服务
    """
    # 返回时会增加额外字段，传入的数据需要和返回时的数据保持一致。
    raw_old_content = service_model.content if service_model.content else []
    old_content = []
    for item in raw_old_content:
        content = get_project_service_content(service_model, item)
        old_content.append(content)

    service_member = ProjectServiceMember.objects.filter(project_service=service_model, deleted=False)

    new_list = []
    update_list = []
    delete_list = []

    member_add_list = []
    member_delete_list = []
    member_update_list = []

    # 通过 'id' 进行比对
    for item_b in model_content:

        if not item_b.get("id"):
            new_list.append(item_b)
            item_b['id'] = str(uuid.uuid4().hex)
        else:
            for item_a in old_content:
                if item_a["id"] == item_b["id"] and item_a != item_b:
                    update_list.append(item_b)

    # 查找 old_content 中在 model_content 中不存在的项
    for item_a in old_content:
        if not any(item_a["id"] == item_b["id"] for item_b in model_content):
            delete_list.append(item_a)

    for item in update_list:
        idx = next(i for i, x in enumerate(old_content) if x["id"] == item["id"])
        member_update_list.append({"index": idx, "new_value": item})

    for item in delete_list:
        idx = old_content.index(item)
        member_delete_list.append(idx)

    for item in new_list:
        member_add_list.append(item)

    for member in service_member.all():

        member_service = member.member_service

        for item in member_update_list:
            index = member_service.object_ids[item.get('index')]
            content = item.get('new_value')
            state = project_service_content_validate(service_model, [content], member_service.project_member, False)
            if state:
                raise WisdomValidationError(state)
            project_member_service_content_update(member_service, index, content)

        del_index = []
        for item in member_delete_list:
            index = member_service.object_ids[item]
            del_project_member_service(member_service, index)
            del_index.append(index)
        object_ids = member_service.object_ids if member_service.object_ids else []
        new_lst = [element for element in object_ids if element not in del_index]
        if member_add_list:
            state = project_service_content_validate(service_model, member_add_list, member_service.project_member)
            if state:
                raise WisdomValidationError(state)

            object_ids = project_member_service_content_create(member_add_list, member_service)
            new_lst += [str(i) for i in object_ids]
        member_service.object_ids = new_lst
        member_service.save()


def get_member_stage_data(stage_content_objects, stage=None):
    item_stage_data = {
        'stage_id': stage.id if stage else None,
        'stage_name': stage.stage_name if stage else None,
        'stage_content': [],
    }

    for item in stage_content_objects:

        raw_data = {
            'content_type': item.content_type,
            'content': [],
            'id': item.id
        }

        if item.object_ids:
            for object_id in item.object_ids:

                content = get_project_member_service_content(item, object_id)
                if content:
                    raw_data['content'].append(content)
        item_stage_data['stage_content'].append(raw_data)
    return item_stage_data


def get_stage_data(stage_content_objects, stage=None):
    item_stage_data = {
        'stage_id': str(stage.id) if stage else None,
        'stage_name': stage.stage_name if stage else None,
        'stage_content': [],
    }

    for stage_content in stage_content_objects:
        member_ids = ProjectServiceMember.objects.filter(project_service=stage_content, deleted=False).values_list(
            'member_service_id', flat=True)
        raw_data = {
            'content_type': stage_content.content_type,
            'content': [],
            'fit_user_ids': list(member_ids),
            'id': stage_content.id,
            'tip_text': ''
        }
        if stage_content.content:
            raw_data['tip_text'] = get_tip_text(stage_content)
            for item in stage_content.content:
                content = get_project_service_content(stage_content, item)
                if content:
                    raw_data['content'].append(content)
        item_stage_data['stage_content'].append(raw_data)

    return item_stage_data


def get_tip_text(stage_content):
    """
    根据阶段内容的类型，检查并生成提示文本，指出哪些客户的教练或利益相关者尚未配置。

    :param stage_content: 阶段内容对象，包含类型和项目服务成员信息。
    :return: str, 格式化的提示文本，列出未配置教练或利益相关者的客户名单。
    """

    tip_text = []  # 初始化提示文本列表

    # 处理利益相关者访谈类型的内容
    if stage_content.content_type == DataType.stakeholder_interview.value:
        # 获取所有相关联的服务成员
        all_project_member_service = stage_content.project_service_members.filter(
            deleted=False, member_service__object_ids__isnull=False).all()

        not_coach_user_name = []  # 未配置教练的客户用户名列表
        not_stakeholder_user_name = []  # 未配置利益相关者的客户用户名列表

        # 遍历所有服务成员
        for project_member_service in all_project_member_service:
            user_name = project_member_service.member_service.project_member.user.cover_name  # 获取客户用户名

            # 遍历服务成员的所有内容ID
            for content_id in project_member_service.member_service.object_ids:
                # 根据内容ID获取利益相关者访谈模块
                stakeholder_interview_model = StakeholderInterviewModule.objects.filter(
                    id=content_id, deleted=False).first()

                # 检查是否缺少教练配置
                if not stakeholder_interview_model.coach_task.public_attr.user_id:
                    not_coach_user_name.append(user_name)

                # 检查是否缺少利益相关者配置
                if not stakeholder_interview_model.stakeholder_interview.filter(deleted=False).exists():
                    not_stakeholder_user_name.append(user_name)


        # 如果有未配置教练的客户，生成提示文本
        if not_coach_user_name:
            tip_text.append(f'客户 {"，".join(not_coach_user_name)} 的访谈教练还未配置')

        # 如果有未配置利益相关者的客户，生成提示文本
        if not_stakeholder_user_name:
            tip_text.append(f'客户 {"，".join(not_stakeholder_user_name)} 的利益相关者还未配置')

    # 处理教练任务类型的内容
    elif stage_content.content_type == DataType.coach_tasks.value:
        # 获取所有相关联的服务成员
        all_project_member_service = stage_content.project_service_members.filter(
            deleted=False, member_service__object_ids__isnull=False).all()

        not_coach_user_name = {}  # 以任务标题为键，存储未配置教练的客户用户名列表

        # 遍历所有服务成员
        for project_member_service in all_project_member_service:
            user_name = project_member_service.member_service.project_member.user.cover_name  # 获取客户用户名

            # 遍历服务成员的所有内容ID
            for content_id in project_member_service.member_service.object_ids:
                # 根据内容ID获取教练任务
                coach_task = CoachTask.objects.filter(id=content_id, deleted=False).first()

                # 检查是否缺少教练配置
                if not coach_task.public_attr.user_id:
                    # 如果任务标题已存在于字典中，追加客户用户名；否则，创建新条目
                    if coach_task.template.title in not_coach_user_name.keys():
                        not_coach_user_name[coach_task.template.title].append(user_name)
                    else:
                        not_coach_user_name[coach_task.template.title] = [user_name]

                api_action_logging = logging.getLogger('api_action')
                api_action_logging.error(f'not_coach_user_name:{not_coach_user_name}')


        # 如果有未配置教练的客户，生成提示文本
        if not_coach_user_name:
            for title, all_user_name in not_coach_user_name.items():
                tip_text.append(f'客户 {"，".join(all_user_name)} 的{title}任务，教练还未配置')

    # 返回格式化后的提示文本，各条目间以换行符分隔
    return '\n'.join(tip_text)


def get_project_member_service_content_all(project_member_id):
    """
    获取项目成员服务内容
    :param project_member_id: 项目成员ID
    :return: 项目成员服务内容
    """

    project_member = ProjectMember.objects.filter(id=project_member_id, deleted=False).first()
    if not project_member:
        return {'is_stage': None, 'service_content': []}

    data = {
        'is_stage': project_member.is_stage if project_member else None,
        'service_content': []
    }

    if project_member.is_stage:
        if ProjectServiceStage.objects.filter(project_member_id=project_member_id, deleted=False).exists():
            stages = ProjectServiceStage.objects.filter(project_member_id=project_member_id,
                                                        deleted=False).order_by('order').all()
            for stage in stages:
                stage_content_objects = stage.project_member_service_content.filter(project_member_id=project_member_id, deleted=False).order_by(
                    '-created_at')
                data['service_content'].append(get_member_stage_data(stage_content_objects, stage))

        else:
            stages = ProjectServiceStage.objects.filter(
                deleted=False, project_id=project_member.project_id).order_by('order')
            for stage in stages:
                # 检查ProjectServiceMember关联，以决定排序
                stage_content_objects = stage.project_member_service_content.annotate(
                    related_order=Coalesce('project_service_members__project_service__order', Value(999))  # 假定一个较大的默认值作为无order的替代
                ).filter(project_member_id=project_member_id, deleted=False).order_by('related_order')
                data['service_content'].append(get_member_stage_data(stage_content_objects, stage))
    else:
        # 不分阶段的逻辑，同样应用排序规则
        service_content = ProjectMemberServiceContent.objects.filter(
            project_member_id=project_member_id, deleted=False
        ).annotate(
            related_order=Coalesce('project_service_members__project_service__order', Value(999))
        ).order_by('related_order')
        if service_content:
            data['service_content'].append(get_member_stage_data(service_content))
    return data



def get_project_service_content_all(project_id):
    """
    获取项目服务内容
    :param project_id: 项目ID
    :return: 项目服务内容
    """
    stage = ServiceStage.objects.filter(deleted=False, project_id=project_id).first()
    data = {
        'is_stage': stage.is_stage if stage else None,
        'service_content': []
    }

    if ProjectServiceStage.objects.filter(project_id=project_id, deleted=False).exists():
        stages = ProjectServiceStage.objects.filter(project_id=project_id, deleted=False).order_by('order').all()
        for stage in stages:
            stage_content_objects = stage.project_service_content.filter(deleted=False).order_by('order')
            data['service_content'].append(get_stage_data(stage_content_objects, stage))
    else:
        stage_content_objects = ProjectServiceContent.objects.filter(
            project_id=project_id, deleted=False).order_by('order')
        if stage_content_objects:
            data['service_content'].append(get_stage_data(stage_content_objects))
    return data


def get_project_service_content(project_service, content):
    """
    获取项目服务内容
    """
    if project_service.content_type == DataType.article.value:
        article = Article.objects.filter(id=content.get('article_id', -1), deleted=False).first()
        if article:
            content['name'] = article.title

    if project_service.content_type == DataType.evaluation.value:
        evaluation = Evaluation.objects.filter(id=content.get('evaluation_id', -1), deleted=False).first()
        if evaluation:
            content['name'] = evaluation.name

    if project_service.content_type == DataType.stakeholder_interview.value:
        coach_template = InterviewRecordTemplate.objects.filter(id=content.get('coach_template_id', -1),
                                                                deleted=False).first()
        if coach_template:
            content['coach_template_name'] = coach_template.name
        report_template = TotalTemplate.objects.filter(id=content.get('report_template_id', -1), deleted=False).first()
        if report_template:
            content['report_template_name'] = report_template.title

    if project_service.content_type == DataType.coach_tasks.value:
        coach_task_template = TotalTemplate.objects.filter(id=content.get('template_id', -1), deleted=False).first()
        if coach_task_template:
            content['template_name'] = coach_task_template.title

    if project_service.content_type in [DataType.group_coach.value, DataType.group_tutoring.value]:
        template = InterviewRecordTemplate.objects.filter(id=content.get('template_id', -1), deleted=False).first()
        if template:
            content['template_name'] = template.name
        coach = Coach.objects.filter(id=content.get('coach_id', -1), deleted=False).first()
        if coach:
            content['coach_name'] = coach.user.cover_name
    return content


def get_project_member_service_content(project_content, object_id):
    """
    获取项目成员服务内容
    :param project_content: 项目成员服务内容
    :param object_id: 项目成员服务内容id
    :return: 项目成员服务内容 dict
    """

    if project_content.content_type == DataType.article.value:
        article_modele = ArticleModule.objects.filter(id=object_id, deleted=False).first()
        if article_modele:
            return {
                'id': article_modele.id,
                'article_id': article_modele.article.id,
                'name': article_modele.article.title,
                'start_date': article_modele.start_time,
                'end_date': article_modele.end_time,
            }
    if project_content.content_type == DataType.evaluation.value:
        evaluation_modele = EvaluationModule.objects.filter(id=object_id, deleted=False).first()
        if evaluation_modele:
            view_permission = DataViewPermission.objects.filter(type=DataTypeEnum.evaluation.value,
                                                                data_id=evaluation_modele.id).first()
            return {
                'id': evaluation_modele.id,
                'evaluation_id': evaluation_modele.evaluation.id,
                'name': evaluation_modele.evaluation.name,
                'start_date': evaluation_modele.start_time,
                'end_date': evaluation_modele.end_time,
                'is_open_screen': evaluation_modele.is_open_screen,
                'role': evaluation_modele.evaluation.role,
                'view_permission': eval(view_permission.role)
            }

    if project_content.content_type == DataType.chemical_interview.value:
        chemical_interview_modele = ChemicalInterviewModule.objects.filter(id=object_id, deleted=False).first()
        if chemical_interview_modele:
            serializer = ChemicalInterviewModuleSerializer(chemical_interview_modele).data
            serializer['start_date'] = serializer.get('start_time')
            serializer['end_date'] = serializer.get('end_time')

            return serializer

    if project_content.content_type == DataType.stakeholder_interview.value:
        stakeholder_interview_modele = StakeholderInterviewModule.objects.filter(id=object_id, deleted=False).first()
        if stakeholder_interview_modele:
            serializer = StakeholderInterviewModuleSerializer(stakeholder_interview_modele).data
            return serializer

    if project_content.content_type == DataType.coach_tasks.value:
        coach_task_modele = CoachTask.objects.filter(id=object_id, deleted=False).first()
        if coach_task_modele:

            # 展示教练信息
            if coach_task_modele.public_attr.user_id:
                coach = Coach.objects.filter(user_id=coach_task_modele.public_attr.user_id, deleted=False).first()
                coach_id = coach.id if coach else None
                coach_name = coach.user.cover_name if coach else None
            else:
                coach_id = None
                coach_name = None

            return {
                "id": coach_task_modele.id,
                'data_type': DataType.coach_tasks.value,
                "created_at": coach_task_modele.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "hours": coach_task_modele.hours,
                "coach_id": coach_id,
                "coach_name": coach_name,
                'template_id': coach_task_modele.template.id if coach_task_modele.template else None,
                'template_name': coach_task_modele.template.title if coach_task_modele.template else None,
            }

    if project_content.content_type == DataType.interview.value:
        one_to_one_interview = OneToOneCoach.objects.filter(id=object_id, deleted=False).first()
        if one_to_one_interview:
            data = {
                'id': one_to_one_interview.id,
                "is_limitation_time": True if one_to_one_interview.online_available_coaching_duration else False,
                "suggested_interval": one_to_one_interview.suggested_interval,
                "suggested_duration": one_to_one_interview.suggested_duration,
                "suggested_start_date": one_to_one_interview.suggested_start_date,
                "hours": one_to_one_interview.online_available_time if one_to_one_interview.online_available_time else one_to_one_interview.online_time,
                "coach_match_type": one_to_one_interview.coach_match_type
            }
            return data

    if project_content.content_type == DataType.growth_goals.value:
        growth_target = GrowthGoalsModule.objects.filter(id=object_id, deleted=False).first()
        if growth_target:
            data = {
                "id": growth_target.id,
                'type': growth_target.type,
                "start_date": growth_target.start_date,
                "end_date": growth_target.end_date,
                "hours": growth_target.hours,
            }
            return data

    if project_content.content_type in [DataType.group_coach.value, DataType.group_tutoring.value]:
        group_coach = GroupCoach.objects.filter(id=object_id, deleted=False).first()
        group_coach_data = group_coach_public.get_group_coach_details(group_coach)
        return group_coach_data

    if project_content.content_type == DataType.change_observation.value:
        change_observation = ChangeObservation.objects.filter(id=object_id, deleted=False).first()
        if change_observation:
            serializer = ChangeObservationDetailSerializer(change_observation)
            return serializer.data
    return


def move_service_stage(current_stage, move_direction):
    """
    移动项目服务阶段
    :param current_stage: 当前阶段
    :param move_direction: 移动方向（1：上移，2：下移）
    :return: None
    """

    if move_direction == 1:  # Move up
        # 获取上一个阶段
        previous_stage = ProjectServiceStage.objects.filter(
            project_id=current_stage.project_id, project_member=current_stage.project_member_id,
            order__lt=current_stage.order).order_by('-order').first()
        if previous_stage:
            previous_stage.order, current_stage.order = current_stage.order, previous_stage.order
            previous_stage.save()
            current_stage.save()

    elif move_direction == 2:  # Move down
        # 获取下一个阶段
        next_stage = ProjectServiceStage.objects.filter(
            project_id=current_stage.project_id, project_member=current_stage.project_member_id,
            order__gt=current_stage.order).order_by('order').first()
        if next_stage:
            next_stage.order, current_stage.order = current_stage.order, next_stage.order
            next_stage.save()
            current_stage.save()


def move_service_model(current_model, move_direction, move_distance):
    """
    移动项目服务模块
    :param current_model: 当前服务模块
    :param move_direction: 移动方向（1：上移，2：下移）
    :param move_distance: 移动步长
    :return: None
    """
    # 获取所有相关模块，并按order排序
    services_list = list(ProjectServiceContent.objects.filter(
        project=current_model.project,
        service_stage=current_model.service_stage,
        deleted=False
    ).order_by('order'))

    # 找到当前模块的索引
    current_index = services_list.index(current_model)

    if move_direction == 1:  # 上移
        # 计算新的索引位置
        new_index = max(current_index - move_distance, 0)
    elif move_direction == 2:  # 下移
        # 计算新的索引位置
        new_index = min(current_index + move_distance, len(services_list) - 1)
    else:
        return  # 不处理其他情况

    # 如果需要移动模块
    if current_index != new_index:
        # 抽取当前模块
        moving_item = services_list.pop(current_index)
        # 插入到新位置
        services_list.insert(new_index, moving_item)

        # 更新所有模块的order值，保持连续性
        for i, service in enumerate(services_list):
            service.order = i + 1  # 从1开始编号
            service.save()


def get_project_service_interview_time(project_id):
    """
        获取项目成员配置时长
        :param project_id: 项目id
        :return: 项目成员配置时长/h
    """
    project = Project.objects.filter(id=project_id, deleted=False).first()

    one_to_one_time = OneToOneCoach.objects.filter(
        project_bundle__project_id=project_id, deleted=False).aggregate(total_online_time=Sum('online_time'))
    total_time = one_to_one_time.get('total_online_time', 0)

    all_times = project.all_times if project.all_times else 0
    total_time = total_time if total_time else 0
    return all_times, total_time


# 内容数据校验
def project_service_content_validate(service_model, model_content, project_member, is_add=True):
    """
    项目服务内容数据校验
    :param service_model: 项目服务模型
    :param model_content: 项目服务内容
    :param project_member: 项目成员
    :param is_add: 是否新增
    """

    is_project = isinstance(service_model, ProjectServiceContent)

    if service_model.content_type == DataType.coach_tasks.value:
        for item in model_content:
            template_id = item.get('template_id')
            hours = item.get('hours')
            if not template_id or hours is None:
                return '缺少必填参数'

            item['hours'] = float(item['hours'])
            hours = item['hours']
            if type(hours) != float:
                return '教练任务时长输入格式错误'
            if str(hours).split('.')[1] not in ['0', '5']:
                return '教练任务时长小数位应为0或5'
            if hours < 0:
                return '教练任务时长应为正数'

    elif service_model.content_type == DataType.evaluation.value:
        is_open_screen_count = 0
        for item in model_content:

            evaluation_id = item.get('evaluation_id')
            start_date = item.get('start_date')
            end_date = item.get('end_date')
            is_open_screen = item.get('is_open_screen')
            if not evaluation_id or not start_date or not end_date:
                return '缺少必填参数'
            if start_date > end_date:
                return '开始时间不能大于结束时间'
            if project_member and is_add:
                if EvaluationModule.objects.filter(
                        evaluation_id=evaluation_id, deleted=False,
                        project_bundle__project_member=project_member).exists():
                    return f'{project_member.user.cover_name}用户同一测评已存在'
            if is_open_screen == 1:
                is_open_screen_count += 1
        if is_open_screen_count > 1:
            return '当前项目下同时只能存在一个开启的开屏测评'

    elif service_model.content_type == DataType.growth_goals.value:
        for item in model_content:
            growth_goals_type = item.get('type')
            if not growth_goals_type:
                return '成长目标类型不能为空'
            if growth_goals_type == GrowthGoalsModelTypeEnum.limit.value:
                if not item.get('hours'):
                    return '缺少时长参数'
            if growth_goals_type == GrowthGoalsModelTypeEnum.fixed.value:
                if not item.get('start_date') or not item.get('end_date'):
                    return '缺少开始时间或结束时间参数'
                if item.get('start_date') > item.get('end_date'):
                    return '开始时间不能大于结束时间'

    elif service_model.content_type in [DataType.group_coach.value, DataType.group_tutoring.value]:
        for item in model_content:
            theme = item.get('theme')
            start_course_time = item.get('start_course_time')
            end_course_time = item.get('end_course_time')
            course_place = item.get('course_place')
            template_id = item.get('template_id')
            if not theme or not start_course_time or not end_course_time or not course_place or not template_id:
                return '缺少必填参数'
            if 'theme' in item.keys():
                if len(item['theme']) > 32:
                    return '辅导主题最多32个字'
            else:
                return '请输入辅导主题'

            if 'course_place' in item.keys():
                if len(item['course_place']) > 40:
                    return '上课地点最多40个字'
            else:
                return '请输入上课地点'

            if start_course_time > end_course_time:
                return '开始时间不能大于结束时间'

    elif service_model.content_type == DataType.interview.value:

        for item in model_content:
            suggested_interval = item.get('suggested_interval')
            suggested_duration = item.get('suggested_duration')
            suggested_start_date = item.get('suggested_start_date')
            coach_match_type = item.get('coach_match_type')
            hours = item.get('hours')
            if not all([suggested_interval, suggested_duration, suggested_start_date, hours, coach_match_type]):
                return '缺少必填参数'
            if hours % suggested_duration != 0:
                return '辅导时长必须是建议辅导时间的整数倍'
            if is_project:
                all_times = service_model.project.all_times
                all_times = all_times if all_times else 0
                if all_times < hours:
                    return '单人辅导时长不能大于项目总时长'

    elif service_model.content_type == DataType.article.value:
        for item in model_content:
            article_id = item.get('article_id')
            end_date = item.get('end_date')
            start_date = item.get('start_date')
            if not article_id or not end_date or not start_date:
                return '缺少必填参数'
            if start_date > end_date:
                return '开始时间不能大于结束时间'

    elif service_model.content_type == DataType.change_observation.value:
        for item in model_content:
            write_condition = item.get('write_condition')
            invite_type = item.get('invite_type')
            if (write_condition != 0 and not write_condition) or type(write_condition) not in [int, float]:
                return '填写条件信息错误'
            if write_condition < 0 or write_condition > 50:
                return '填写条件小时数在0-50之间'
            remind_type = item.get('remind_type')
            if not remind_type or type(remind_type) != list:
                return '提醒方式错误'

            if not is_project:
                growth_goals = item.get('growth_goals')
                if not growth_goals:
                    return '成长目标数据错误'
                max_stakeholders_count = item.get('max_stakeholders_count')
                if not max_stakeholders_count:
                    return '必须填写需要调研的利益相关者数量'
                project_interested = item.get('project_interested')
                # 如果没有通知对象类型（默认发送给利益相关者）
                # 如果类型通知对象等于利益相关者，就必须配置利益相关者
                if (not invite_type or invite_type == ChangeObservationInviteTypeEnum.stakeholders.value) and not project_interested:
                    return '利益相关者数据错误'
    elif service_model.content_type == DataType.chemical_interview.value:
        if project_member:
            if project_member.chemical_interview.filter(deleted=False).exists() and is_add:
                raise WisdomValidationError('当前被教练者已配置化学面谈，请勿重复配置')

        for item in model_content:
            max_interview_number = item.get('max_interview_number', 0)
            if max_interview_number and max_interview_number not in [1, 2, 3, 4, 5]:
                return '最多面谈5次最少1次'
            duration = item.get('duration')
            if duration not in [0.5, 1, 1.5, 2]:
                return '化学面谈时长仅支持 0.5, 1, 1.5, 2小时'

            if not is_project:
                coach_source = item.get('coach_source')
                coach = item.get('coach')
                if coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                    if not coach:
                        return '系统随机教练来源时必须选择教练'
    elif service_model.content_type == DataType.stakeholder_interview.value:
        if project_member:
            if project_member.stakeholder_interview_module.filter(deleted=False).exists() and is_add:
                raise WisdomValidationError('当前被教练者已配置利益相关者访谈，请勿重复配置')

        for item in model_content:
            if item['stakeholder_interview_number'] not in list(range(1, 11)):
                return '访谈人数只支持1-10人'
            if item['duration'] not in [0.5, 1, 1.5, 2]:
                return '利益相关者访谈时长仅支持 0.5, 1, 1.5, 2小时'
            if not InterviewRecordTemplate.objects.filter(id=item['coach_template_id'],
                                                          deleted=False).exists():
                return '未找到访谈记录模版'
            if not TotalTemplate.objects.filter(id=item['report_template_id'], deleted=False).exists():
                return '未找到访谈报告模版'

            stakeholder = []
            if 'stakeholder' in item.keys():
                stakeholder = item.get('stakeholder')
            if stakeholder and project_member:
                stakeholder_ids = list(ProjectInterested.objects.filter(
                    master_id=project_member.user_id, project_id=project_member.project_id,
                    deleted=False).values_list('id', flat=True))
                if not check_elements_exist(stakeholder, stakeholder_ids):  # 检查传入的利益相关者关系id是否在被教练者的所有利益相关者关系id中
                    return '参与访谈的人员错误'
            if 'coach_template_id' in item.keys():
                if not InterviewRecordTemplate.objects.filter(id=item['coach_template_id'],
                                                              deleted=False).exists():
                    raise WisdomValidationError('未找到访谈记录模版')
            if 'report_template_id' in item.keys():
                if not TotalTemplate.objects.filter(id=item['report_template_id'], deleted=False).exists():
                    raise WisdomValidationError('未找到访谈报告模版')


def project_member_service_content_update(service_model, content_id, content, is_project=True):
    """
    项目成员服务内容更新
    :param service_model: 服务模型
    :param content_id: 服务内容id
    :param content: 服务内容
    :return: None
    """

    project = service_model.project_member.project
    project_member = service_model.project_member
    if service_model.content_type == DataType.article.value:
        article_module = ArticleModule.objects.filter(id=content_id, deleted=False)
        article_module.update(
            article_id=content.get('article_id'), start_time=content.get('start_date'),
            end_time=content.get('end_date'))
    elif service_model.content_type == DataType.coach_tasks.value:

        template = TotalTemplate.objects.filter(pk=content.get('template_id')).first()
        # 任务类型
        if template and template.write_role == InterviewRecordTemplateRoleEnum.stakeholder:
            task_type = NewCoachTaskTypeEnum.stakeholder_research
        else:
            task_type = NewCoachTaskTypeEnum.default_task

        coach_task = CoachTask.objects.filter(id=content_id, deleted=False).first()
        coach_task.hours = content.get('hours')
        coach_task.template_id = content.get('template_id')
        coach_task.type = task_type
        coach_task.save()

        # 获取教练用户id
        coach_user_id = content.get('coach_id')
        # 如果没有教练用户id，并且原来的教练任务没有，则查询用户是否匹配教练
        if not coach_user_id and not coach_task.public_attr.user_id:
            # 用户匹配教练，更新教练任务的默认教练
            coach_user_id = project_member.coach_id
            if coach_user_id:
                coach_task_public.update_coach_task_coach_user_id(coach_task, coach_user_id)
        # 如果教练用户id不等于已有的教练用户id，进行更新操作。
        elif coach_user_id and coach_user_id != coach_task.public_attr.user_id:
            coach_task_public.update_coach_task_coach_user_id(coach_task, coach_user_id)

    elif service_model.content_type == DataType.evaluation.value:
        evaluation = EvaluationModule.objects.filter(id=content_id, deleted=False).first()
        e_id = evaluation.evaluation_id
        evaluation.start_time = content.get("start_date")
        evaluation.end_time = content.get('end_date')
        evaluation.evaluation_id = content.get('evaluation_id')
        evaluation.is_open_screen = content.get('is_open_screen')
        evaluation.save()
        if evaluation.evaluation_id != e_id and evaluation.evaluation.role == EvaluationWriteRoleEnum.coachee_stakeholder.value:
            center.send_evaluation_message.apply_async(kwargs=dict(
                project_member_id=service_model.project_member.pk, evaluation_module_id=evaluation.id), countdown=3,
                expires=120)

        DataViewPermission.objects.filter(data_id=evaluation.id,
                                          type=DataTypeEnum.evaluation.value,
                                          deleted=False).update(role=str(content.get('view_permission')))

    elif service_model.content_type == DataType.growth_goals.value:
        growth_goals = GrowthGoalsModule.objects.filter(id=content_id, deleted=False)
        growth_goals.update(
            start_date=content.get('start_date'), end_date=content.get('end_date'),
            type=content.get('type'), hours=content.get('hours'))
    elif service_model.content_type == DataType.stakeholder_interview.value:
        instance = StakeholderInterviewModule.objects.filter(id=content_id, deleted=False).first()
        stakeholder = content.get('stakeholder')
        coach_user_id = content.get('coach_id')

        if stakeholder:
            err, deleted_list, add_list = stakeholder_interview_public.check_update_stakeholder(
                stakeholder, instance, content.get('stakeholder_interview_number'))
            if err:
                raise WisdomValidationError(err)
            stakeholder_interview_public.update_stakeholder_interview(add_list, deleted_list, instance)

        if 'coach_template_id' in content.keys():
            if content['coach_template_id'] != instance.coach_template_id:
                # 修改了访谈记录模版，检查是否存在教练已填写的利益相关者访谈
                stakeholder_interviews = instance.stakeholder_interview.filter(interview__isnull=False,
                                                                               deleted=False)
                if stakeholder_interviews.exists():
                    for stakeholder_interview in stakeholder_interviews:
                        if InterviewRecordTemplateAnswer.objects.filter(
                                interview=stakeholder_interview.interview, deleted=False).exists():
                            raise WisdomValidationError('当前访谈记录模版教练已填写，禁止修改')
        if 'report_template_id' in content.keys():
            if content['report_template_id'] != instance.report_template_id:
                # 修改利益相关者访谈报告模版（教练任务模版），检查是否存在教练已填写的利益相关者访谈报告
                if InterviewRecordTemplateAnswer.objects.filter(public_attr=instance.coach_task.public_attr,
                                                                deleted=False).exists():
                    raise WisdomValidationError('当前访谈报告模版教练已填写，禁止修改')

        exists_coach_template_id = instance.coach_template_id
        exists_report_template_id = instance.report_template_id
        instance.stakeholder_interview_number = content.get('stakeholder_interview_number')
        instance.duration = content.get('duration')
        instance.start_date = content.get('start_date')
        instance.end_date = content.get('end_date')
        instance.coach_template_id = content.get('coach_template_id')
        instance.report_template_id = content.get('report_template_id')
        instance.save()

        if content.get('coach_template_id') != exists_coach_template_id:
            # 修改模板删除缓存id
            for item in instance.stakeholder_interview.filter(
                    deleted=False, interview__isnull=False).all():
                UserTmp.objects.filter(
                    data_id=item.interview_id, type=UserTmpEnum.interview,
                    extra_id=exists_coach_template_id
                ).delete()

        report_template_id = content.get('report_template_id')
        coach_task = instance.coach_task
        if exists_report_template_id != report_template_id:
            # 修改模板删除缓存id
            UserTmp.objects.filter(data_id=coach_task.id, type=UserTmpEnum.coach_tasks).delete()
            coach_task.template_id = report_template_id
            coach_task.save()

        # 如果没有教练用户id，并且原来的教练任务没有，则查询用户是否匹配教练
        if not coach_user_id and not coach_task.public_attr.user_id:
            # 用户匹配教练，更新教练任务的默认教练
            coach_user_id = project_member.coach_id
            if coach_user_id:
                coach_task_public.update_coach_task_coach_user_id(coach_task, coach_user_id)
        # 如果教练用户id不等于已有的教练用户id，进行更新操作。
        elif coach_user_id and coach_user_id != coach_task.public_attr.user_id:
            coach_task_public.update_coach_task_coach_user_id(coach_task, coach_user_id)

    elif service_model.content_type == DataType.chemical_interview.value:
        instance = ChemicalInterviewModule.objects.filter(pk=content_id, deleted=False).first()
        coach = content.get('coach')
        max_interview_number = content.get('max_interview_number')
        if isinstance(coach, list):
            exists_coach_ids = list(instance.coaches.filter(deleted=False).values_list('coach_id', flat=True))
            deleted_list = list(set(exists_coach_ids).difference(set(coach)))
            add_list = list(set(coach).difference(set(exists_coach_ids)))
            if deleted_list:
                for coach_id in deleted_list:
                    chemical_interview = ChemicalInterview2Coach.objects.filter(
                        coach_id=coach_id, chemical_interview_module=instance,
                        deleted=False, interview__isnull=False).first()
                    if chemical_interview:
                        raise WisdomValidationError(
                            f'用户已与{chemical_interview.coach.user.cover_name}教练预约化学面谈，'
                            f'不可删除')
                ChemicalInterview2Coach.objects.filter(
                    coach_id__in=deleted_list,
                    chemical_interview_module=instance,
                    deleted=False, interview__isnull=True).update(deleted=True)

            if add_list:
                for coach_id in add_list:
                    ChemicalInterview2Coach.objects.create(
                        chemical_interview_module=instance, coach_id=coach_id,
                        chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)
        else:
            # 面谈教练来源改变
            if instance.coach_source != content.get('coach_source'):
                # 如果是系统随机 则自动分配教练
                if content.get('coach_source') == ChemicalInterviewCoachSourceEnum.system_random.value:
                    coaches = get_random_coach(
                        instance.project_member.pk, instance.project_member.project_id, max_interview_number)
                    for coach in coaches:
                        ChemicalInterview2Coach.objects.create(
                            coach=coach, chemical_interview_module=instance,
                            chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback
                        )
                # 如果是自主选择，将之前之前绑定的教练移除
                if content.get('coach_source') == ChemicalInterviewCoachSourceEnum.auto_select.value:
                    if ChemicalInterview2Coach.objects.filter(
                        chemical_interview_module=instance, deleted=True, interview__isnull=False
                    ).exists():
                        raise WisdomValidationError('用户已预约化学面谈，不可修改教练来源')

                    ChemicalInterview2Coach.objects.filter(
                        chemical_interview_module=instance, deleted=False).update(deleted=True)

        instance.max_interview_number = max_interview_number
        instance.duration = content.get('duration')
        instance.start_time = content.get('start_date')
        instance.end_time = content.get('end_date')
        instance.coach_source = content.get('coach_source')
        instance.save()

    elif service_model.content_type == DataType.change_observation.value:
        instance = ChangeObservation.objects.filter(pk=content_id, deleted=False).first()
        is_write = ChangeObservationAnswer.objects.filter(change_observation=instance).exists()
        growth_goals = content.get('growth_goals')
        project_interested = content.get('project_interested')
        remind_type = content.get('remind_type')

        stakeholders_write_end_date_str = content.get('stakeholders_write_end_date')
        stakeholders_write_end_date = datetime.strptime(stakeholders_write_end_date_str, '%Y-%m-%d').date()
        instance.invite_type = content.get('invite_type')
        instance.write_condition = content.get('write_condition')
        instance.stakeholders_write_end_date = stakeholders_write_end_date
        instance.max_stakeholders_count = content.get('max_stakeholders_count')
        instance.remind_type = remind_type
        instance.invite_end_time = content.get('invite_end_time')
        instance.save()

        if not is_project:
            exists_growth_goals = list(GrowthGoals2ChangeObservation.objects.filter(
                change_observation=instance, deleted=False).values_list('growth_goals_id', flat=True))
            new_growth_goals = [g['id'] for g in growth_goals]
            deleted_list = list(set(exists_growth_goals).difference(set(new_growth_goals)))
            if deleted_list:
                if is_write:
                    raise WisdomValidationError('当前改变观察已填写，无法修改')
                GrowthGoals2ChangeObservation.objects.filter(
                    growth_goals_id__in=deleted_list, change_observation=instance,
                    deleted=False).update(deleted=True)
            for g in growth_goals:
                growth_goals2change_observation = GrowthGoals2ChangeObservation.objects.filter(
                    growth_goals_id=g['id'], change_observation=instance, deleted=False).first()
                if growth_goals2change_observation:
                    if growth_goals2change_observation.change != g['change'] and is_write:
                        raise WisdomValidationError('当前改变观察已填写，无法修改')
                    growth_goals2change_observation.change = g['change']
                    growth_goals2change_observation.save()
                else:
                    if not is_write:
                        GrowthGoals2ChangeObservation.objects.create(growth_goals_id=g['id'],
                                                                     change=g['change'],
                                                                     change_observation=instance)
                    else:
                        raise WisdomValidationError('当前改变观察已填写，无法修改')

            exists_project_interested = list(MultipleAssociationRelation.objects.filter(
                main_id=instance.pk,
                type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
                deleted=False).values_list('secondary_id', flat=True))
            deleted_list = list(set(exists_project_interested).difference(set(project_interested)))
            add_list = list(set(project_interested).difference(set(exists_project_interested)))
            if deleted_list:
                MultipleAssociationRelation.objects.filter(
                    main_id=instance.pk, secondary_id__in=deleted_list,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
                    deleted=False).update(deleted=True)
            if add_list:
                data_list = [MultipleAssociationRelation(
                    main_id=instance.pk, secondary_id=p, deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value) for p in
                    add_list]
                MultipleAssociationRelation.objects.bulk_create(data_list)

            # 只有利益相关者通知触发自动发送
            if int(instance.invite_type) == ChangeObservationInviteTypeEnum.stakeholders.value:
                interview_time = interview_public.get_user_interview_time(instance.project_member)
                if instance.write_condition == 0 or interview_time >= instance.write_condition:
                    task.send_change_observation_stakeholder_notice.delay(instance)

            # 修改人数后是否生成报告
            answer_count = ChangeObservationAnswer.objects.filter(change_observation=instance).count()
            if instance.max_stakeholders_count > answer_count:
                instance.is_complete = False
            elif instance.max_stakeholders_count == answer_count and not instance.deleted and not instance.is_complete:
                task.generate_change_observation_report.apply_async(kwargs=dict(
                    change_observation_id=instance.pk), countdown=3)
                instance.is_complete = True
            instance.save()

    elif service_model.content_type == DataType.interview.value:
        # 获取当前1-1辅导配置
        interview = OneToOneCoach.objects.filter(pk=content_id, deleted=False).first()
        correcting_time = interview.online_time

        # 获取项目配置时长和用户总配置时长
        all_times, total_time = get_project_service_interview_time(service_model.project_member.project_id)

        # 获取用户已有辅导时长
        interview_time = interview_public.get_user_reservation_interview_time(service_model.project_member)

        # 获取用户配置的辅导时长
        user_total_time = one_to_one_coach_public.get_project_member_one_to_one_time(
            interview.project_bundle.project_member)

        # 用户已预约辅导时长 > 当前用户总配置辅导时长 - 修改前辅导时长 + 修改后的辅导时长
        if interview_time > (user_total_time - correcting_time + content.get('hours', 0)):
            raise WisdomValidationError(f'用户当前已预约辅导时间大于配置的可用时长')

        # 项目配置辅导时长 > 全部用户总配置时长 - 修改前辅导时长 + 修改后的辅导时长
        if all_times < (total_time - correcting_time + content.get('hours', 0)):
            raise WisdomValidationError(f'用户配置的辅导时长超过了项目总时长')

        interview.online_available_time = content.get('hours')
        interview.online_time = content.get('hours')
        interview.suggested_interval = content.get('suggested_interval')
        interview.suggested_duration = content.get('suggested_duration')
        interview.suggested_start_date = content.get('suggested_start_date')
        interview.coach_match_type = content.get('coach_match_type')

        interview.save()
    elif service_model.content_type == DataType.group_coach.value:
        now_group_coach = GroupCoach.objects.filter(pk=content_id, deleted=False).first()
        group_coach_public.update_group_coach_details(content, now_group_coach)

    elif service_model.content_type == DataType.group_tutoring.value:
        now_group_coach = GroupCoach.objects.filter(pk=content_id, deleted=False).first()
        group_coach_public.update_group_coach_details(content, now_group_coach)


def project_member_service_content_create(contents, service_content, is_project=True):
    """
    项目成员服务内容创建
    :param contents: 服务内容
    :param service_content: 服务内容对象
    :param is_project: 是否是项目
    :return: 新增对象列表
    """
    obj_ids = []
    project_member = service_content.project_member

    project_bundle = ProjectBundle.objects.filter(project_member=project_member, deleted=False).first()
    if not project_bundle:
        project_bundle = ProjectBundle.objects.create(project=project_member.project, project_member=project_member)

    if service_content.content_type == DataType.article.value:
        for content in contents:
            obj = ArticleModule.objects.create(
                article_id=content.get('article_id'), start_time=content.get('start_date'),
                end_time=content.get('end_date'), project_bundle=project_bundle)
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.coach_tasks.value:

        # 查询项目用户是否匹配教练
        project_coach_id = project_member.coach_id

        for content in contents:

            # 利益相关者访谈优先使用指定教练id，没有指定教练id时使用项目匹配的教练id
            coach_user_id = None
            coach_id = content.get('coach_id') if content.get('coach_id') else project_coach_id
            if coach_id:
                coach_user = Coach.objects.filter(id=content.get('coach_id'), deleted=False).first()
                if coach_user:
                    coach_user_id = coach_user.user_id

            obj = project_member_public.add_coach_task(
                coach_user_id, content.get('template_id'),
                content.get('hours'), project_member, project_bundle)
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.interview.value:
        for content in contents:
            all_times, total_time = get_project_service_interview_time(project_member.project_id)
            if all_times < (total_time + content.get('hours', 0)):
                raise WisdomValidationError(f'用户配置的辅导时长超过了项目总时长')
            obj = OneToOneCoach.objects.create(**{
                "type": CoachTypeEnum.online,
                "online_available_time": content.get('hours'),
                "suggested_interval": content['suggested_interval'],
                "suggested_duration": content['suggested_duration'],
                "project_bundle_id": project_bundle.id,
                "online_available_coaching_duration": 1,  # 限制时长
                "online_coaching_ceiling": 0,  # 每周辅导次数上限无限制
                "suggested_start_date": content['suggested_start_date'],
                "online_time": content.get('hours'),
                "coach_match_type": content.get('coach_match_type'),
            })
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.evaluation.value:
        for content in contents:
            data_dict = {
                "evaluation_id": content['evaluation_id'],
                "start_time": content["start_date"],
                "end_time": content["end_date"],
                "project_bundle_id": project_bundle.id,
                "is_open_screen": content['is_open_screen']
            }
            obj = EvaluationModule.objects.create(**data_dict)
            if obj.evaluation.role == EvaluationWriteRoleEnum.coachee_stakeholder.value:
                center.send_evaluation_message.apply_async(kwargs=dict(
                    project_member_id=project_member.id, evaluation_module_id=obj.id), countdown=3, expires=120)
            # 设置测评查看权限
            DataViewPermission.objects.create(
                type=DataTypeEnum.evaluation.value, data_id=obj.id, role=str(content['view_permission']))
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.growth_goals.value:
        for content in contents:
            obj = GrowthGoalsModule.objects.create(
                type=content.get('type'), hours=content.get('hours'), project_bundle=project_bundle,
                start_date=content.get('start_date'), end_date=content.get('end_date'))
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.group_coach.value:
        for content in contents:
            data = {
                'start_course_time':  content['course_date'] + ' ' + content['start_course_time'] + ':00',
                'end_course_time': content['course_date'] + ' ' + content['end_course_time'] + ':00',
                'course_place': content['course_place'],
                'theme': content['theme'],
                'template_id': content['template_id'],
                'coach_id': content['coach_id'],
                'power_tag': content['power_tag'],
                'project_group_coach_id': content.get('project_group_coach_id'),
            }
            obj = group_coach_public.create_group_coach_details(data, project_bundle, GroupCoachTypeEnum.collective_tutoring.value)
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.group_tutoring.value:
        for content in contents:
            data = {
                'start_course_time': content['course_date'] + ' ' + content['start_course_time'] + ':00',
                'end_course_time': content['course_date'] + ' ' + content['end_course_time'] + ':00',
                'course_place': content['course_place'],
                'theme': content['theme'],
                'template_id': content['template_id'],
                'coach_id': content['coach_id'],
                'power_tag': content['power_tag'],
                'project_group_coach_id': content.get('project_group_coach_id'),
            }
            obj = group_coach_public.create_group_coach_details(data, project_bundle, GroupCoachTypeEnum.group_tutoring.value)
            obj_ids.append(obj.id)
    elif service_content.content_type == DataType.change_observation.value:

        for content in contents:
            write_condition = content.get('write_condition')
            remind_type = content.get('remind_type')
            growth_goals = content.get('growth_goals', [])
            project_interested = content.get('project_interested', [])
            invite_end_time = content.get('invite_end_time')
            stakeholders_write_end_date_str = content.get('stakeholders_write_end_date')
            stakeholders_write_end_date = datetime.strptime(stakeholders_write_end_date_str, '%Y-%m-%d').date()
            invite_type = content.get('invite_type')
            max_stakeholders_count = content.get('max_stakeholders_count')
            name = get_change_observation_title(project_member)
            instance = ChangeObservation.objects.create(
                remind_type=remind_type, name=name, invite_end_time=invite_end_time,
                stakeholders_write_end_date=stakeholders_write_end_date,
                invite_type=invite_type, max_stakeholders_count=max_stakeholders_count,
                project_member=project_member, write_condition=write_condition)
            instance.save()
            for g in growth_goals:
                GrowthGoals2ChangeObservation.objects.create(change_observation=instance, growth_goals_id=g['id'],
                                                             change=g['change'])

            for p in project_interested:
                MultipleAssociationRelation.objects.create(
                    main_id=instance.pk, secondary_id=p,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value)

            if not is_project:
                if not instance.project_member.project.manager_list:
                    raise WisdomValidationError('当前项目没有项目顾问，无法发送提醒')

                if int(instance.invite_type) == ChangeObservationInviteTypeEnum.stakeholders.value:
                    interview_time = interview_public.get_user_interview_time(project_member)
                    if write_condition == 0 or interview_time >= write_condition:
                        task.send_change_observation_stakeholder_notice.delay(instance)
            obj_ids.append(instance.id)

    elif service_content.content_type == DataType.chemical_interview.value:
        for content in contents:
            max_interview_number = content.get('max_interview_number', 0)
            duration = content.get('duration')
            coach_source = content.get('coach_source')
            coach = content.get('coach')
            start_time = content.get('start_date')
            end_time = content.get('end_date')

            instance = ChemicalInterviewModule.objects.create(
                project_member=project_member, max_interview_number=max_interview_number, duration=duration,
                start_time=start_time, end_time=end_time, coach_source=coach_source)
            if coach:
                for c in coach:
                    # 状态为待定，辅导时间接结束后修改为未反馈
                    ChemicalInterview2Coach.objects.create(
                        chemical_interview_module=instance, coach_id=c,
                        chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)

            else:
                # 如果是系统随机 则自动分配教练
                if coach_source == ChemicalInterviewCoachSourceEnum.system_random.value:
                    coaches = get_random_coach(project_member.pk, project_member.project_id, max_interview_number)
                    for coach in coaches:
                        ChemicalInterview2Coach.objects.create(
                            coach=coach, chemical_interview_module=instance,
                            chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback
                        )
            obj_ids.append(instance.id)
    elif service_content.content_type == DataType.stakeholder_interview.value:

        project_coach_id = project_member.coach_id

        for content in contents:
            stakeholder = []
            if 'stakeholder' in content.keys():
                stakeholder = content.pop('stakeholder')

            # 利益相关者访谈优先使用指定教练id，没有指定教练id时使用项目匹配的教练id
            coach_user_id = None
            coach_id = content.get('coach_id') if content.get('coach_id') else project_coach_id
            if coach_id:
                coach_user = Coach.objects.filter(id=content.get('coach_id'), deleted=False).first()
                if coach_user:
                    coach_user_id = coach_user.user_id

            # 创建教练任务
            coach_task = project_member_public.add_coach_task(coach_user_id, content.get('report_template_id'), 0, project_member, project_bundle)
            content['coach_task_id'] = coach_task.id
            # 创建利益相关者访谈配置
            instance = StakeholderInterviewModule.objects.create(
                project_member=project_member,
                stakeholder_interview_number=content.get('stakeholder_interview_number'),
                duration=content.get('duration'),
                start_date=content.get('start_date'),
                end_date=content.get('end_date'),
                coach_template_id=content.get('coach_template_id'),
                report_template_id=content.get('report_template_id'),
                coach_task=coach_task
            )
            if stakeholder:
                stakeholder_interview_list = []
                for project_interested_id in stakeholder:
                    stakeholder_interview_list.append(
                        StakeholderInterview(stakeholder_interview_module=instance,
                                             project_interested_id=project_interested_id))
                # 创建利益相关者访谈
                StakeholderInterview.objects.bulk_create(stakeholder_interview_list)
            obj_ids.append(instance.id)

    return obj_ids


# 个人视角项目服务路径图
def get_path_diagram(project_member, role=None, is_5alc=True, coach_user_id=None):
    """
    个人视角项目服务路径图
    :param project_member: 项目成员
    :param role: 角色 教练请求时传入
    :param is_5alc: 是否是5alc教练
    :param coach_user_id: 教练用户id
    """

    if project_member.project.start_time and project_member.project.end_time:
        project_date = f"{project_member.project.start_time.strftime('%Y-%m-%d')} ~ {project_member.project.end_time.strftime('%Y-%m-%d')}"
    else:
        project_date = ''

    if project_member.project.is_show_path_diagram_hour:
        user_total_time = one_to_one_coach_public.get_project_member_one_to_one_time(project_member)
    else:
        user_total_time = None

    data = {
        'stage_data': [],
        'project_name': f"{project_member.project.name}项目安排",
        'project_date': project_date,
        'interview_hour': user_total_time
    }

    if not project_member.is_stage:  # 服务阶段为空，不分阶段
        data['is_stage'] = False
        project_member_content = ProjectMemberServiceContent.objects.filter(project_member=project_member,
                                                                            deleted=False)
        if not project_member_content:
            return None
        member_stage = [list(project_member_content)]
    else:
        data['is_stage'] = True
        stages = ProjectServiceStage.objects.filter(project_member=project_member, deleted=False)
        if stages.exists():  # 用户自定义阶段
            stage_ids = list(stages.order_by('order').values_list('id', flat=True))

            member_stage = [list(ProjectMemberServiceContent.objects.filter(
                project_member=project_member, service_stage_id=stage_id, deleted=False)) for stage_id in stage_ids]
        else:  # 取项目阶段
            project_stage = ProjectServiceStage.objects.filter(project=project_member.project, deleted=False)
            if not project_stage:
                return None
            stage_ids = list(project_stage.order_by('order').values_list('id', flat=True))
            member_stage = [list(ProjectMemberServiceContent.objects.filter(
                project_member=project_member, service_stage_id=stage_id, deleted=False)) for stage_id in stage_ids]

    for index, stage in enumerate(member_stage):  # 遍历每个阶段的内容
        if not stage:  # 阶段下服务内容为空
            # if not data['is_stage']:
            #     data['stage_data'].append({'name': None, 'content': [], 'status': 1})
            # else:
            #     tmp_stage = ProjectServiceStage.objects.filter(id=stage_ids[index]).first()
            #     stage_name = tmp_stage.stage_name if tmp_stage else None
            #     data['stage_data'].append({'name': stage_name, 'content': [], 'status': 1})
            continue

        content_data = {"name": None, "content": [], "status": 1}  # 阶段内容
        for content in stage:
            if data['is_stage']:
                content_data['name'] = content.service_stage.stage_name

            if not content.object_ids:  # 服务内容
                continue
            if content.content_type == DataType.article:
                # 去掉只能查看自己匹配客户的限制
                # if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                #     continue
                article_data = get_member_service_article_data(content.object_ids)
                content_data['content'].extend(article_data)
            elif content.content_type == DataType.evaluation:
                # 去掉只能查看自己匹配客户的限制
                # if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                #     continue
                evaluation_data = get_member_service_evaluation_data(content.object_ids, role, project_member, is_5alc)
                content_data['content'].extend(evaluation_data)
            elif content.content_type == DataType.chemical_interview:
                chemical_interview_data = get_member_service_chemical_interview_data(content.object_ids, role,
                                                                                     project_member, coach_user_id)
                content_data['content'].extend(chemical_interview_data)
            elif content.content_type == DataType.stakeholder_interview:
                if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                    continue
                stakeholder_interview_data = get_member_service_stakeholder_interview_data(content.object_ids, role,
                                                                                           project_member,  coach_user_id)
                content_data['content'].extend(stakeholder_interview_data)
            elif content.content_type == DataType.coach_tasks:
                if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                    continue
                coach_task_data = get_member_service_coach_task_data(content.object_ids, role, project_member, is_5alc)
                content_data['content'].extend(coach_task_data)
            elif content.content_type == DataType.interview:
                # 去掉只能查看自己匹配客户的限制
                # if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                #     continue
                one_to_one_interview_data = get_member_service_one_to_one_interview_data(content.object_ids, role,
                                                                                         project_member,
                                                                                         data['is_stage'], coach_user_id)
                one_to_one_interview_data = [interview_data for interview_data in one_to_one_interview_data
                                             if interview_data not in content_data['content']]
                content_data['content'].extend(one_to_one_interview_data)
            elif content.content_type == DataType.growth_goals:
                if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                    continue
                growth_target_data = get_member_service_growth_target_data(content.object_ids, role, project_member)
                content_data['content'].extend(growth_target_data)
            elif content.content_type == DataType.group_coach:

                if is_5alc:
                    if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                        continue
                else:
                    if coach_user_id and not get_mop_project_coach_exists(project_member.project, coach_user_id):
                        continue
                workshop_data = get_member_service_workshop_data(content.object_ids, role, project_member)
                content_data['content'].extend(workshop_data)
            elif content.content_type == DataType.group_tutoring:

                if is_5alc:
                    if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                        continue
                else:
                    if coach_user_id and not get_mop_project_coach_exists(project_member.project, coach_user_id):
                        continue
                group_tutoring_data = get_member_service_group_tutoring_data(content.object_ids, role, project_member)
                content_data['content'].extend(group_tutoring_data)
            elif content.content_type == DataType.change_observation:
                if coach_user_id and not get_project_coach_exists(project_member, coach_user_id):
                    continue
                change_observation_data = get_member_service_change_observation_data(content.object_ids, project_member,
                                                                                     role, is_5alc)
                content_data['content'].extend(change_observation_data)

        content_data = content_data_order(content_data)
        data['stage_data'].append(content_data)

    return data


def get_member_service_change_observation_data(change_observation_ids, project_member, role, is_5alc):
    """
    获取个人改变观察调研数据
    :param change_observation_ids: 改变观察调研id列表
    :param project_member: 项目成员
    :param role: 角色 教练请求时传入
    :param is_5alc: 是否5alc教练

    change_observation_data参数详解
    data_type：数据类型，前端用来区分卡片跳转页面
    date：用来展示的时间
    report_id：已完成的生成的报告id
    order_time: 在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用（排序时间和辅导排序时间高度绑定）
    start_date: 卡片的开始时间（开始时间和辅导高度绑定，已完成的不能直接报告生成时间，需要通过辅导时间计算开始时间）
    """
    data = []
    for change_observation in ChangeObservation.objects.filter(id__in=change_observation_ids, deleted=False):
        change_observation_data = {"data_type": DataType.change_observation, "name": "改变观察调研",
                                   "module_id": change_observation.id, "date": None, "report_id": None}
        report = PersonalReport.objects.filter(type=PersonalReportTypeEnum.change_observation_report,
                                               object_id=change_observation.id, user=project_member.user,
                                               project=project_member.project, deleted=False).first()

        # 获取项目成员服务的辅导x小时后配置模块的建议时间，排序时间，开始日期
        # 建议时间用来展示，排序时间用来计算卡片顺序，开始日期用来计算卡片状态
        change_observation_date, order_time, start_date = get_member_service_interview_date(
            project_member, change_observation.write_condition)
        if report:
            change_observation_data['report_id'] = report.id
            change_observation_data['date'] = report.created_at.strftime('%Y-%m-%d %H:%M')
            change_observation_data['order_time'] = datetime.combine(order_time, datetime.min.time()) if order_time else report.created_at
            change_observation_data['start_date'] = datetime.combine(start_date, datetime.min.time()) if start_date else report.created_at
        else:
            if not change_observation_date:  # 改变观察无法触发，不展示
                change_observation_data['date'] = change_observation.created_at.strftime('%Y-%m-%d')
                change_observation_data['order_time'] = change_observation.created_at
                change_observation_data['start_date'] = change_observation.created_at
            else:
                change_observation_data['date'] = change_observation_date
                change_observation_data['order_time'] = datetime.combine(order_time, datetime.min.time())
                change_observation_data['start_date'] = datetime.combine(start_date, datetime.min.time())

        # 有报告或者当前时间大于开始时间 = 已完成
        change_observation_data['is_complete'] = True if (
                report or change_observation_data['start_date'].date() <= datetime.now().date()) else False

        if change_observation_data['start_date'].date() - timedelta(days=3) >= datetime.now().date():
            change_observation_data['status'] = 1  # 未解锁
        elif datetime.now() < change_observation_data['start_date'] and not change_observation_data['report_id']:
            change_observation_data['status'] = 2  # 已解锁 且 当前时间小于任务的开始时间 且未生成报告
        elif datetime.now() >= change_observation_data['start_date'] and not change_observation_data['report_id']:
            change_observation_data['status'] = 3  # 已解锁 且 当前时间已经大于等于任务的开始时间 且未生成报告
        elif datetime.now() >= change_observation_data['start_date'] and change_observation_data['report_id']:
            change_observation_data['status'] = 4  # 已解锁 且 当前时间已经大于等于任务的开始时间 且已生成报告
        else:
            change_observation_data['status'] = 1  # 未解锁

        if role and not is_5alc:
            change_observation_data['status'] = 11  # 集体辅导教练不展示改变观察反馈
        data.append(change_observation_data)

    return data


def get_member_service_article_data(article_ids):
    """
    获取个人课前阅读数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param article_ids: 课前阅读id列表
    """
    exists_date = []
    data = []
    for article_module in ArticleModule.objects.filter(id__in=article_ids):
        article_data = {"data_type": DataType.article, "module_id": article_module.id,
                        "name": '课前阅读',
                        "date": f"{article_module.start_time.strftime('%Y-%m-%d')}~"
                                f"{article_module.end_time.strftime('%Y-%m-%d')}",
                        "is_complete": True if article_module.start_time <= datetime.now().date() else False,
                        "order_time": datetime.combine(article_module.start_time, datetime.min.time())}
        if article_module.start_time - timedelta(days=3) >= datetime.now().date():
            article_data['status'] = 1
        else:
            article_data['status'] = 2
        if article_data['date'] not in exists_date:
            data.append(article_data)
            exists_date.append(article_data['date'])
    return data


def get_member_service_evaluation_data(evaluation_ids, role, project_member, is_5alc):
    """
    获取个人测评数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param evaluation_ids: 测评id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员
    :param is_5alc: 是否5alc教练
    """
    data = []
    evaluation_modules = EvaluationModule.objects.filter(id__in=evaluation_ids, deleted=False)
    if not evaluation_modules:
        return data
    qr_code_url = get_project_manage_qr_code(project_member.project_id)
    for evaluation_module in evaluation_modules:
        evaluation_date = f"{evaluation_module.start_time.strftime('%Y-%m-%d')}~" \
                          f"{evaluation_module.end_time.strftime('%Y-%m-%d')}"

        evaluation_report_config = evaluation_module.evaluation.evaluation_report_config.filter(deleted=False).first()
        evaluation_data = {"data_type": DataType.evaluation, "module_id": evaluation_module.id,
                           "date": evaluation_date, "name": evaluation_module.evaluation.name,
                           "evaluation_code": evaluation_module.evaluation.code,
                           "evaluation_id": evaluation_module.evaluation_id,
                           'evaluation_report_type': evaluation_report_config.type if evaluation_report_config else None}
        report = EvaluationReport.objects.filter(
            evaluation=evaluation_module.evaluation, public_attr__user=project_member.user,
            public_attr__project=project_member.project, deleted=False).first()
        evaluation_data['report_id'] = report.id if report else None
        evaluation_data['is_complete'] = True if datetime.now().date() >= evaluation_module.start_time else False
        evaluation_data['order_time'] = datetime.combine(evaluation_module.start_time, datetime.min.time())
        evaluation_data['qr_code_url'] = qr_code_url
        # 已解锁：当前时间 > 任务开始时间-3天
        # 未解锁：当前时间 <= 任务开始时间-3天
        if role:
            if evaluation_module.start_time - timedelta(days=3) >= datetime.now().date():
                evaluation_data['status'] = 1  # 未解锁
            elif not report and datetime.now().date() < evaluation_module.start_time:
                evaluation_data['status'] = 2  # 已解锁 且 未生成报告 且 当前时间小于测评开始时间
            elif not report and datetime.now().date() >= evaluation_module.start_time:
                evaluation_data['status'] = 3  # 已解锁 且 未生成报告 且 当前时间大于测评开始时间
            elif report:
                evaluation_data['status'] = 4  # 已解锁 且 已生成报告

            if is_5alc:  # 5alc教练
                if evaluation_data['evaluation_code'] == MANAGE_EVALUATION:
                    evaluation_data['status'] = 11  # 教练不可查看报告
            else:  # 集体辅导教练
                evaluation_data['status'] = 11  # 教练不可查看报告

        else:
            if evaluation_module.start_time - timedelta(days=3) >= datetime.now().date():
                evaluation_data['status'] = 1  # 未解锁
            elif datetime.now().date() < evaluation_module.start_time:
                evaluation_data['status'] = 2  # 已解锁 当前时间小于任务开始时间
            elif not evaluation_module.is_submit and \
                    evaluation_module.start_time <= datetime.now().date() <= evaluation_module.end_time:
                evaluation_data['status'] = 3  # 已解锁 未完成且当前时间大于等于任务的开始时间，小于任务的结束时间时
            elif evaluation_module.is_submit and report:
                evaluation_data['status'] = 4  # 已解锁 且 已完成 且 已生成报告
            elif evaluation_module.is_submit and not report:
                evaluation_data['status'] = 5  # 已解锁 且 已完成 且 未生成报告
            elif not evaluation_module.is_submit and datetime.now().date() > evaluation_module.end_time:
                evaluation_data['status'] = 6  # 已解锁 且 未完成 且 当前时间大于任务的结束时间
            else:
                evaluation_data['status'] = 1
        data.append(evaluation_data)
    return data


def get_member_service_chemical_interview_data(chemical_interview_ids, role, project_member, coach_user_id):
    """
    获取个人化学面谈数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param chemical_interview_ids: 化学面谈id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员
    :param coach_user_id: 教练用户id
    """
    data = []
    qr_code_url = get_project_manage_qr_code(project_member.project_id)

    if coach_user_id:

        select_exists = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module_id__in=chemical_interview_ids,
            interview__public_attr__user_id=coach_user_id,
            chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).exists()
        chemical_interview_all = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module_id__in=chemical_interview_ids,
            interview__public_attr__user_id=coach_user_id,
            deleted=False).order_by('interview__public_attr__start_time')
    else:
        select_exists = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module_id__in=chemical_interview_ids,
            chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).exists()
        chemical_interview_all = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module_id__in=chemical_interview_ids, interview__isnull=False,
            deleted=False).order_by('interview__public_attr__start_time')

    # 化学面谈预约入口
    next_chemical_data = None
    for index, chemical_interview in enumerate(chemical_interview_all):

        chemical_interview_data = {
            "data_type": DataType.chemical_interview,
            "module_id": chemical_interview.chemical_interview_module.id,
            "interview_id": chemical_interview.interview_id,
            "chemical_interview_id": chemical_interview.id, "qr_code_url": qr_code_url,
            'date': f"{chemical_interview.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-"
                    f"{chemical_interview.interview.public_attr.end_time.strftime('%H:%M')}",
            'meeting_info': interview_public.get_interview_meeting_info(chemical_interview.interview),
            'order_time': chemical_interview.interview.public_attr.start_time,
            'name': f"与{chemical_interview.coach.user.cover_name}教练的化学面谈" if not role
            else f"与{chemical_interview.chemical_interview_module.project_member.user.cover_name}客户的化学面谈"}
        chemical_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                         chemical_interview_data['order_time'].date() else False
        if not role:
            if chemical_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                chemical_interview_data['status'] = 1  # 未解锁
            elif datetime.now() < chemical_interview.interview.public_attr.start_time - timedelta(minutes=30):
                chemical_interview_data['status'] = 5  # 已解锁 且 已预约面谈 且 当前时间< 面谈开始时间-30分钟
            elif chemical_interview.interview.public_attr.start_time - timedelta(minutes=30) <= datetime.now() < \
                    chemical_interview.interview.public_attr.end_time:
                chemical_interview_data['status'] = 6  # 已解锁 且 已预约面谈 且 当前时间大约等于面谈开始时间-30分钟，小于辅导结束时间
            elif datetime.now() >= chemical_interview.interview.public_attr.end_time:
                if chemical_interview.chemical_interview_status == ChemicalInterviewStatusEnum.not_feedback:
                    chemical_interview_data['status'] = 7  # 已解锁 且 已预约面谈 且 当前时间>=面谈结束时间 且未填写反馈
                else:
                    chemical_interview_data['status'] = 8  # 已解锁 且 已预约面谈 且 当前时间>=面谈结束时间 且已填写反馈
                    
                # 未选择教练时候，在最后一次化学面谈下方再生成1条化学面谈的任务，展示为未预约状态
                if not select_exists:
                    # 只有当（不选择）是最后一条辅导数据的时候，才需要展示。
                    if index + 1 == len(chemical_interview_all):
                        next_chemical_data = {
                            "data_type": DataType.chemical_interview,
                            "module_id": chemical_interview.chemical_interview_module.id,
                            "interview_id": chemical_interview.interview_id,
                            "chemical_interview_id": None,
                            "qr_code_url": qr_code_url,
                            "date": f"{chemical_interview.chemical_interview_module.start_time.strftime('%Y-%m-%d')}~"
                                    f"{chemical_interview.chemical_interview_module.end_time.strftime('%Y-%m-%d')}",
                            "order_time": chemical_interview_data['order_time'] + timedelta(minutes=1),
                            "name": "化学面谈",
                            "status": 3  # # 已解锁 且未预约教练 且 当前时间 大于等于 化学面谈开始日期 小于等于化学面谈的结束日期
                        }
                        next_chemical_data['is_complete'] = True if \
                            datetime.now().date() >= next_chemical_data['order_time'].date() else False
                        if datetime.now().date() > chemical_interview.chemical_interview_module.end_time:
                            next_chemical_data['status'] = 7

        else:
            if chemical_interview.chemical_interview_status != ChemicalInterviewStatusEnum.selected:
                continue
            if chemical_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                chemical_interview_data['status'] = 1  # 未解锁
            elif datetime.now() < chemical_interview.interview.public_attr.start_time - timedelta(minutes=30):
                chemical_interview_data['status'] = 3  # 已解锁 且 有客户预约 且 当前时间< 面谈开始时间-30分钟
            elif chemical_interview.interview.public_attr.start_time - timedelta(minutes=30) <= datetime.now() < \
                    chemical_interview.interview.public_attr.end_time:
                chemical_interview_data['status'] = 4  # 已解锁 且 已预约面谈 且 当前时间大约等于面谈开始时间-30分钟，小于辅导结束时间
            elif datetime.now() >= chemical_interview.interview.public_attr.end_time and not chemical_interview.interview.coach_record_status:
                chemical_interview_data['status'] = 5  # 已解锁 且 已预约面谈 且 当前时间>=面谈结束时间 且未填写记录
            elif datetime.now() >= chemical_interview.interview.public_attr.end_time and chemical_interview.interview.coach_record_status:
                chemical_interview_data['status'] = 6  # 已解锁 且 已预约面谈 且 当前时间>=面谈结束时间 且已填写记录
        data.append(chemical_interview_data)

    if next_chemical_data:
        data.append(next_chemical_data)

    if not data:  # 化学面谈还没有配置面谈对象
        chemical_interview_module = ChemicalInterviewModule.objects.filter(id__in=chemical_interview_ids).first()
        if chemical_interview_module:
            chemical_interview_data = {"data_type": DataType.chemical_interview,
                                       "module_id": chemical_interview_module.id,
                                       "interview_id": None,
                                       "chemical_interview_id": None,
                                       "qr_code_url": qr_code_url,
                                       "date": f"{chemical_interview_module.start_time.strftime('%Y-%m-%d')}~"
                                               f"{chemical_interview_module.end_time.strftime('%Y-%m-%d')}",
                                       "order_time": datetime.combine(chemical_interview_module.start_time,
                                                                      datetime.min.time()),
                                       "name": "化学面谈",
                                       "status": 1}
            chemical_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                             chemical_interview_data['order_time'].date() else False
            if not role:
                # if chemical_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                #     chemical_interview_data['status'] = 1  # 未解锁
                # elif datetime.now().date() < chemical_interview_module.start_time:
                #     chemical_interview_data['status'] = 2  # 已解锁 且未预约教练 且 当前时间 < 化学面谈开始日期
                # 当前时间在结束时间之前就可以预约化学面谈
                if datetime.now().date() <= chemical_interview_module.end_time:
                    chemical_interview_data['status'] = 3  # 已解锁 且未预约教练 且 当前时间 大于等于 化学面谈开始日期 小于等于化学面谈的结束日期
                elif datetime.now().date() > chemical_interview_module.end_time:
                    chemical_interview_data['status'] = 4  # 已解锁 且未预约教练 且 当前时间 大于 化学面谈的结束日期
                # else:
                #     if chemical_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                #         chemical_interview_data['status'] = 1  # 未解锁
                #     elif datetime.now().date() <= chemical_interview_module.end_time:
                #         chemical_interview_data['status'] = 2  # 已解锁 且 无客户预约 且 当前时间小于等于化学面谈的结束日期
                #     elif datetime.now().date() > chemical_interview_module.end_time:
                #         chemical_interview_data['status'] = 7  # 已解锁 且 无客户预约 且 当前时间大于化学面谈的结束日期
                data.append(chemical_interview_data)
    return data


def get_member_service_stakeholder_interview_data(stakeholder_interview_ids, role, project_member,  coach_user_id):
    """
    获取项目成员的利益相关者访谈数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    stakeholder_interview_ids 利益相关者访谈模块id列表
    role 角色，教练请求时传入， 用户请求时不传入
    project_member 项目成员
    """
    data = []

    if coach_user_id:
        stakeholder_interview_module_all = StakeholderInterviewModule.objects.filter(
            id__in=stakeholder_interview_ids, coach_task__public_attr__user_id=coach_user_id, deleted=False)
    else:
        stakeholder_interview_module_all = StakeholderInterviewModule.objects.filter(
            id__in=stakeholder_interview_ids, deleted=False)

    for stakeholder_interview_module in stakeholder_interview_module_all:
        stakeholder_interview_data = {"module_id": stakeholder_interview_module.id,
                                      "data_type": DataType.stakeholder_interview, "name": "利益相关者访谈",
                                      "coach_task_id": stakeholder_interview_module.coach_task_id,
                                      "interview_id": None}
        if stakeholder_interview_module.coach_task.coach_submit_time:
            stakeholder_interview_data['date'] = stakeholder_interview_module.coach_task. \
                coach_submit_time.strftime('%Y-%m-%d %H:%M')
            stakeholder_interview_data['order_time'] = stakeholder_interview_module.coach_task \
                .coach_submit_time
        else:
            stakeholder_interview_data['date'] = f"{stakeholder_interview_module.start_date.strftime('%Y-%m-%d')}~" \
                                                 f"{stakeholder_interview_module.end_date.strftime('%Y-%m-%d')}"
            stakeholder_interview_data['order_time'] = datetime.combine(
                stakeholder_interview_module.start_date, datetime.min.time())

        if not role:
            if stakeholder_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                stakeholder_interview_data['status'] = 1  # 未解锁
                stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= stakeholder_interview_data[
                    'order_time'].date() else False
                data.append(stakeholder_interview_data)
            elif not stakeholder_interview_module.coach_task.report_url:
                if datetime.now().date() < stakeholder_interview_module.start_date:
                    stakeholder_interview_data['status'] = 2  # 已解锁 且 未生成报告 且 当前时间 < 利益相关者访谈的开始日期
                    stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                        stakeholder_interview_data[
                                                                            'order_time'].date() else False
                    data.append(stakeholder_interview_data)
                elif stakeholder_interview_module.start_date <= datetime.now().date() <= stakeholder_interview_module.end_date:
                    stakeholder_interview_data['status'] = 3  # 已解锁 且 未生成报告  且 当前时间 大于等于 利益相关者访谈开始日期 小于等于利益相关者访谈的结束日期
                    stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                        stakeholder_interview_data[
                                                                            'order_time'].date() else False
                    data.append(stakeholder_interview_data)
                elif datetime.now().date() > stakeholder_interview_module.end_date:
                    stakeholder_interview_data['status'] = 4  # 已解锁 且 未生成报告 且 当前时间 大于 利益相关者访谈的结束日期
                    stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                        stakeholder_interview_data[
                                                                            'order_time'].date() else False
                    data.append(stakeholder_interview_data)
            elif stakeholder_interview_module.coach_task.report_url:
                stakeholder_interview_data['status'] = 5  # 已解锁 且 已生成报告
                stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= stakeholder_interview_data[
                    'order_time'].date() else False
                data.append(stakeholder_interview_data)
        else:
            if stakeholder_interview_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
                stakeholder_interview_data['status'] = 1  # 未解锁
                stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= stakeholder_interview_data[
                    'order_time'].date() else False
                data.append(stakeholder_interview_data)
            else:
                if stakeholder_interview_module.stakeholder_interview.filter(interview__isnull=False,
                                                                             deleted=False).exists():  # 有利益相关者预约
                    for stakeholder_interview in stakeholder_interview_module.stakeholder_interview.filter(
                            interview__isnull=False, deleted=False):  # 查询已预约的利益相关者访谈
                        stakeholder_interview_data = {"module_id": stakeholder_interview_module.id,
                                                      "data_type": DataType.stakeholder_interview, "name": "利益相关者访谈",
                                                      "coach_task_id": stakeholder_interview_module.coach_task_id,
                                                      'meeting_info': interview_public.get_interview_meeting_info(stakeholder_interview.interview),
                                                      "interview_id": None}
                        if datetime.now() < stakeholder_interview.interview.public_attr.start_time - timedelta(
                                minutes=30):
                            stakeholder_interview_data['status'] = 3  # 已解锁 且 有利益相关者预约 且 当前时间< 面谈开始时间-30分钟
                            stakeholder_interview_data[
                                'name'] = f"与{stakeholder_interview.project_interested.interested.cover_name}的利益相关者访谈"
                            stakeholder_interview_data[
                                'date'] = f"{stakeholder_interview.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                          f"{stakeholder_interview.interview.public_attr.end_time.strftime('%H:%M')}"
                            stakeholder_interview_data[
                                'order_time'] = stakeholder_interview.interview.public_attr.start_time
                            stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                                stakeholder_interview_data[
                                                                                    'order_time'].date() else False
                            stakeholder_interview_data['interview_id'] = stakeholder_interview.interview_id
                            data.append(stakeholder_interview_data)
                        elif stakeholder_interview.interview.public_attr.start_time - timedelta(minutes=30) \
                                <= datetime.now() < stakeholder_interview.interview.public_attr.end_time:
                            stakeholder_interview_data['status'] = 4  # 已解锁 且 已预约访谈 且 当前时间大约等于面谈开始时间-30分钟，小于辅导结束时间
                            stakeholder_interview_data[
                                'name'] = f"与{stakeholder_interview.project_interested.interested.cover_name}的利益相关者访谈"
                            stakeholder_interview_data[
                                'date'] = f"{stakeholder_interview.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                          f"{stakeholder_interview.interview.public_attr.end_time.strftime('%H:%M')}"
                            stakeholder_interview_data[
                                'order_time'] = stakeholder_interview.interview.public_attr.start_time
                            stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                                stakeholder_interview_data[
                                                                                    'order_time'].date() else False
                            stakeholder_interview_data['interview_id'] = stakeholder_interview.interview_id
                            data.append(stakeholder_interview_data)
                        elif datetime.now() >= stakeholder_interview.interview.public_attr.end_time and not \
                                stakeholder_interview.interview.coach_record_status:
                            stakeholder_interview_data['status'] = 5  # 已解锁 且 已预约访谈 且 当前时间>=面谈结束时间 且未填写记录
                            stakeholder_interview_data[
                                'name'] = f"与{stakeholder_interview.project_interested.interested.cover_name}的利益相关者访谈"
                            stakeholder_interview_data[
                                'date'] = f"{stakeholder_interview.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                          f"{stakeholder_interview.interview.public_attr.end_time.strftime('%H:%M')}"
                            stakeholder_interview_data[
                                'order_time'] = stakeholder_interview.interview.public_attr.start_time
                            stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                                stakeholder_interview_data[
                                                                                    'order_time'].date() else False
                            stakeholder_interview_data['interview_id'] = stakeholder_interview.interview_id
                            data.append(stakeholder_interview_data)
                        elif datetime.now() >= stakeholder_interview.interview.public_attr.end_time and \
                                stakeholder_interview.interview.coach_record_status:
                            stakeholder_interview_data['status'] = 6  # 已解锁 且 已预约访谈 且 当前时间>=面谈结束时间 且已填写记录
                            stakeholder_interview_data[
                                'name'] = f"与{stakeholder_interview.project_interested.interested.cover_name}的利益相关者访谈"
                            stakeholder_interview_data[
                                'date'] = f"{stakeholder_interview.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                          f"{stakeholder_interview.interview.public_attr.end_time.strftime('%H:%M')}"
                            stakeholder_interview_data[
                                'order_time'] = stakeholder_interview.interview.public_attr.start_time
                            stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                                stakeholder_interview_data[
                                                                                    'order_time'].date() else False
                            stakeholder_interview_data['interview_id'] = stakeholder_interview.interview_id
                            data.append(stakeholder_interview_data)
                else:  # 无利益相关者预约
                    if datetime.now().date() <= stakeholder_interview_module.end_date:
                        stakeholder_interview_data['status'] = 2  # 已解锁 且 无利益相关者预约 且 当前时间小于等于化学面谈的结束日期
                        stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                            stakeholder_interview_data[
                                                                                'order_time'].date() else False
                        data.append(stakeholder_interview_data)
                    elif datetime.now().date() > stakeholder_interview_module.end_date:
                        stakeholder_interview_data['status'] = 7  # 已解锁 且 无利益相关者预约 且 当前时间大于化学面谈的结束日期
                        stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= \
                                                                            stakeholder_interview_data[
                                                                                'order_time'].date() else False
                        data.append(stakeholder_interview_data)

    if role and data:  # 教练侧 在利益相关者访谈 下方展示 访谈配置的教练任务
        data = sorted(data, key=lambda x: x['order_time'], reverse=False)
        last_order_time = data[-1]['order_time']
        for stakeholder_interview_module in StakeholderInterviewModule.objects.filter(
                id__in=stakeholder_interview_ids, deleted=False):
            last_order_time += timedelta(minutes=1)
            stakeholder_interview_data = {
                "module_id": stakeholder_interview_module.id, "data_type": DataType.stakeholder_interview,
                "name": stakeholder_interview_module.coach_task.template.title,
                "coach_task_id": stakeholder_interview_module.coach_task_id, "interview_id": None,
                "order_time": last_order_time}
            stakeholder_interview_data['is_complete'] = True if datetime.now().date() >= stakeholder_interview_data[
                'order_time'].date() else False
            coach_task = stakeholder_interview_module.coach_task
            if coach_task.coach_submit_time:
                stakeholder_interview_data['date'] = coach_task.coach_submit_time.strftime('%Y-%m-%d %H:%M')
                stakeholder_interview_data['status'] = 8  # 已填写，展示“查看报告 >”,点击跳转到教练任务报告查看界面
            else:
                stakeholder_interview_data['date'] = f"{stakeholder_interview_module.start_date.strftime('%Y-%m-%d')}~" \
                                                     f"{stakeholder_interview_module.end_date.strftime('%Y-%m-%d')}"
                stakeholder_interview_data['status'] = 10  # 未填写 且 最后一个面谈已结束 展示点亮版“填写报告”按钮，
                # 点击跳转到教练任务填写页面，自动填充前面总结填写的内容
            data.append(stakeholder_interview_data)
    return data


def get_member_service_coach_task_data(coach_task_ids, role, project_member, is_5alc):
    """
    获取成员服务的教练任务数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param coach_task_ids: 教练任务id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员
    :param is_5alc: 是否5alc教练

    coach_task_data参数详解
    data_type：数据类型，前端用来区分卡片跳转页面
    date：用来展示的时间
    coach_task_type：教练任务分为普通教练任务和利益相关者的教练任务，不同类型不同处理方式
    order_time: 在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用（排序时间和辅导排序时间高度绑定）
    start_date: 卡片的开始时间（开始时间和辅导高度绑定，已完成的不能直接报告生成时间，需要通过辅导时间计算开始时间）
    """
    data = []
    for coach_task in CoachTask.objects.filter(id__in=coach_task_ids, deleted=False):

        is_summary_report_status = coach_task_public.check_coach_task_is_stakeholder_interview_summary(coach_task.id)
        coach_task_data = {"data_type": DataType.coach_tasks, "module_id": coach_task.id,
                           "name": coach_task.template.title, "date": None, "coach_task_type": coach_task.type,
                           'report_url': coach_task.report_url, "query_type": 1 if is_summary_report_status else 2  # 1-查看报告 2-查看详情
                           }

        # 获取项目成员服务的辅导x小时后配置模块的建议时间，排序时间，开始日期
        # 建议时间用来展示，排序时间用来计算卡片顺序，开始日期用来计算卡片状态
        coach_task_date, order_time, start_date = get_member_service_interview_date(project_member, coach_task.hours)

        # 教练任务的完成状态，不同类型的教练任务，判断逻辑不一致，数据处理的时候记录下来，后续用来快速判断是否完成。
        is_complete = False
        if coach_task.type == NewCoachTaskTypeEnum.stakeholder_research and coach_task.stakeholder_submit_time:
            coach_task_data['date'] = coach_task.stakeholder_submit_time.strftime('%Y-%m-%d %H:%M')
            coach_task_data['order_time'] = datetime.combine(order_time, datetime.min.time()) if order_time else datetime.combine(coach_task.stakeholder_submit_time, datetime.min.time())
            coach_task_data['start_date'] = datetime.combine(start_date, datetime.min.time()) if start_date else datetime.combine(coach_task.stakeholder_submit_time, datetime.min.time())
            is_complete = True
        else:
            if coach_task.template.write_role in [InterviewRecordTemplateRoleEnum.coach,
                                                  InterviewRecordTemplateRoleEnum.coach_student] \
                    and coach_task.coach_submit_time:
                coach_task_data['date'] = coach_task.coach_submit_time.strftime('%Y-%m-%d %H:%M')
                coach_task_data['order_time'] = datetime.combine(order_time, datetime.min.time()) if order_time else datetime.combine(
                    coach_task.coach_submit_time, datetime.min.time())
                coach_task_data['start_date'] = datetime.combine(start_date, datetime.min.time()) if start_date else datetime.combine(
                    coach_task.coach_submit_time, datetime.min.time())
                is_complete = True
            if coach_task.template.write_role in [InterviewRecordTemplateRoleEnum.coach_student,
                                                  InterviewRecordTemplateRoleEnum.student] and not role \
                    and coach_task.coachee_submit_time:
                coach_task_data['date'] = coach_task.coachee_submit_time.strftime('%Y-%m-%d %H:%M')
                coach_task_data['order_time'] = datetime.combine(order_time, datetime.min.time()) if order_time else datetime.combine(coach_task.coachee_submit_time, datetime.min.time())
                coach_task_data['start_date'] = datetime.combine(start_date, datetime.min.time()) if start_date else datetime.combine(coach_task.coachee_submit_time, datetime.min.time())
                is_complete = True

        if not coach_task_data['date']:

            if not coach_task_date:  # 教练任务无法触发，不展示
                coach_task_data['date'] = coach_task.created_at.strftime('%Y-%m-%d')
                coach_task_data['order_time'] = coach_task.created_at
                coach_task_data['start_date'] = coach_task.created_at
            else:
                coach_task_data['date'] = coach_task_date
                coach_task_data['order_time'] = datetime.combine(order_time, datetime.min.time())
                coach_task_data['start_date'] = datetime.combine(start_date, datetime.min.time())

        # 已完成或者当时时间大于开始时间，展示已完成
        coach_task_data['is_complete'] = True if (
                is_complete or datetime.now().date() >= coach_task_data['start_date'].date()) else False

        # 没有完成，开始时间比对，是否是解锁状态
        if not is_complete and coach_task_data['start_date'].date() - timedelta(days=3) >= datetime.now().date():
            coach_task_data['status'] = 1  # 未解锁
        else:
            # 已完成或者已解锁，通过更加具体的判断获取状态
            if coach_task.type == NewCoachTaskTypeEnum.stakeholder_research:
                if coach_task.stakeholder_submit_time:  # 报告生成
                    coach_task_data['status'] = 3  # 已解锁 且 当前角色无需填写 且 报告已生成
                else:
                    coach_task_data['status'] = 2  # 已解锁 且 当前角色无需填写 且 报告未生成
            else:
                if role:
                    if not coach_task.report_url:  # 报告未生成
                        if coach_task.template.write_role not in [InterviewRecordTemplateRoleEnum.coach_student,
                                                                  InterviewRecordTemplateRoleEnum.coach]:  # 当前角色无需填写
                            coach_task_data['status'] = 2  # 已解锁 且 当前角色无需填写 且 报告未生成
                        else:
                            coach_task_data['status'] = 4  # 已解锁 且 当前角色需要填写 且 报告未生成
                    else:
                        if coach_task.template.write_role not in [InterviewRecordTemplateRoleEnum.coach_student,
                                                                  InterviewRecordTemplateRoleEnum.coach]:
                            coach_task_data['status'] = 3  # 已解锁 且 当前角色无需填写 且 报告已生成
                        else:
                            coach_task_data['status'] = 5  # 已解锁 且 当前角色需要填写 且 报告已生成
                    # 工作坊教练不能查看教练任务报告
                    if not is_5alc:
                        coach_task_data['status'] = 11
                else:
                    if not coach_task.report_url:  # 报告未生成
                        if coach_task.template.write_role not in [InterviewRecordTemplateRoleEnum.coach_student,
                                                                  InterviewRecordTemplateRoleEnum.student]:  # 当前角色无需填写
                            coach_task_data['status'] = 2  # 已解锁 且 当前角色无需填写 且 报告未生成
                        else:
                            coach_task_data['status'] = 4  # 已解锁 且 当前角色需要填写 且 报告未生成
                    else:
                        if coach_task.template.write_role not in [InterviewRecordTemplateRoleEnum.coach_student,
                                                                  InterviewRecordTemplateRoleEnum.student]:
                            coach_task_data['status'] = 3  # 已解锁 且 当前角色无需填写 且 报告已生成
                        else:
                            coach_task_data['status'] = 5  # 已解锁 且 当前角色需要填写 且 报告已生成
        data.append(coach_task_data)

    return data


def get_member_service_one_to_one_interview_data(one_to_one_interview_ids, role, project_member, is_stage, coach_user_id):
    """
    获取项目成员服务的一对一辅导数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param one_to_one_interview_ids: 一对一辅导id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员
    :param is_stage: 是否是阶段性服务
    :param coach_user_id: 教练用户标识
    """
    one_to_one_interview = OneToOneCoach.objects.filter(id__in=one_to_one_interview_ids, deleted=False)
    if not one_to_one_interview:
        return []
    interview_contents = ProjectMemberServiceContent.objects.filter(
        project_member=project_member, deleted=False,
        content_type=DataType.interview, object_ids__isnull=False
    )
    one_to_one_interview_ids = []
    for interview_content in interview_contents:
        one_to_one_interview_ids.extend(interview_content.object_ids)

    one_to_one_interviews = OneToOneCoach.objects.filter(id__in=one_to_one_interview_ids,
                                                         deleted=False).order_by('suggested_start_date')
    # 修改成获取所有辅导，如果不是教练自己的辅导，就没有action
    interview_list = split_member_service_interview_data(project_member, one_to_one_interviews)
    if is_stage:  # 分阶段取当前阶段的辅导
        tmp_list = [{"id": i.id, "count": math.ceil(i.online_available_time / i.suggested_duration)} for i in
                    one_to_one_interviews]
        index_list = []  # 获取数量切片
        for one_to_one in one_to_one_interview:
            exists_count = 0
            for index, tmp in enumerate(tmp_list):
                if one_to_one.id == tmp['id']:
                    index_list.append([exists_count, exists_count + tmp['count']])
                exists_count += tmp['count']
        if index_list:
            start = index_list[0][0]
            end = index_list[-1][-1]
            interview_list = interview_list[start:end]

    # 是否有未预约的建议辅导时间
    is_proposal_interview = False
    # 只展示一条建议辅导，不需要全部的interview_list，使用新列表接收
    new_interview_list = []
    for interview in interview_list:

        if not interview['interview_id']:
            # 未预约的建议辅导只展示一条数据，如果已有就跳过
            if is_proposal_interview:
                continue
            else:
                # 只展示一条建议辅导卡片，卡片不需要时间判断解锁，固定已解锁 且 未预约辅导
                is_proposal_interview = True
                interview['status'] = 2  # 已解锁 且 未预约辅导
        elif interview['interview_id']:
            project_interview = ProjectInterview.objects.filter(id=interview['interview_id'], deleted=False).first()

            # 客户视角：影子观察 不展示操作按钮
            if coach_user_id and project_interview.public_attr.user_id != coach_user_id:
                interview['status'] = 7  # 客户和其他教练的辅导，仅展示不查看详情。已解锁 不展示任何按钮 点击无任何跳转。
            elif not coach_user_id and interview['interview_subject'] == InterviewSubjectEnum.shadow_observation.value:
                interview['status'] = 7  # 已解锁 不展示任何按钮 点击无任何跳转。
            elif datetime.now() < interview['start_time'] - timedelta(minutes=30):
                interview['status'] = 3  # 已解锁 且 已预约辅导 且 当前时间 < 辅导开始时间-30分钟
            elif interview['start_time'] - timedelta(
                    minutes=30) <= datetime.now() < project_interview.public_attr.end_time:
                interview['status'] = 4  # 已解锁 且 已预约辅导 且 当前时间大约等于辅导开始时间-30分钟，小于辅导结束时间
            elif datetime.now() >= project_interview.public_attr.end_time and not project_interview.coachee_record_status and not role:
                interview['status'] = 5  # 已解锁 且 已预约辅导 且 当前时间>=辅导结束时间 且未填写记录
            elif datetime.now() >= project_interview.public_attr.end_time and not project_interview.coach_record_status and role:
                interview['status'] = 5  # 已解锁 且 已预约辅导 且 当前时间>=辅导结束时间 且未填写记录
            elif datetime.now() >= project_interview.public_attr.end_time and project_interview.coachee_record_status and not role:
                interview['status'] = 6  # 已解锁 且 已预约辅导 且 当前时间>=辅导结束时间 且已填写记录
            elif datetime.now() >= project_interview.public_attr.end_time and project_interview.coach_record_status and role:
                interview['status'] = 6  # 已解锁 且 已预约辅导 且 当前时间>=辅导结束时间 且已填写记录
        interview.pop('hour')
        interview.pop('complete_date')
        new_interview_list.append(interview)
    return new_interview_list


def get_member_service_growth_target_data(growth_target_ids, role, project_member):
    """
    获取项目成员服务的成长目标数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param growth_target_ids: 成长目标id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员

    growth_goals_data参数详解
    data_type：数据类型，前端用来区分卡片跳转页面
    date：用来展示的时间


    order_time: 在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    start_date: 卡片的开始时间
    当成长目标类型为辅导x小时后
    order_time时间，start_date时间和辅导开始时间高度绑定，需要通过get_member_service_interview_date获取辅导时间


    """
    data = []
    growth_goals_modules = GrowthGoalsModule.objects.filter(id__in=growth_target_ids, deleted=False)
    if not growth_goals_modules:
        return data

    for growth_goals_module in growth_goals_modules:
        growth_goals_data = {'data_type': DataType.growth_goals, 'module_id': growth_goals_module.id, 'name': "成长目标"}

        # 目标设置类型为设置起止日期
        # 这类成长目标的排序时间和开始时间使用固定时间
        if growth_goals_module.type == GrowthGoalsModelTypeEnum.fixed:
            if growth_goals_module.growth_goals:
                growth_goals_data["date"] = growth_goals_module.growth_goals.created_at.strftime('%Y-%m-%d %H:%M')
            else:
                growth_goals_data["date"] = f"{growth_goals_module.start_date.strftime('%Y-%m-%d')}~" \
                                            f"{growth_goals_module.end_date.strftime('%Y-%m-%d')}"
            growth_goals_data['order_time'] = datetime.combine(growth_goals_module.start_date, datetime.min.time())
            growth_goals_data['start_date'] = datetime.combine(growth_goals_module.start_date, datetime.min.time())

        # 目标设置类型为辅导x小时候
        # 这类成长目标的排序时间和开始时间需要额外计算
        elif growth_goals_module.type == GrowthGoalsModelTypeEnum.limit:
            # 获取项目成员服务的辅导x小时后配置模块的建议时间，排序时间，开始日期
            # 建议时间用来展示，排序时间用来计算卡片顺序，开始日期用来计算卡片状态
            growth_goals_date, order_time, start_date = get_member_service_interview_date(
                project_member, growth_goals_module.hours)
            if not growth_goals_date:  # 成长目标无法触发，不展示
                continue

            if growth_goals_module.growth_goals:
                growth_goals_data["date"] = growth_goals_module.growth_goals.created_at.strftime('%Y-%m-%d %H:%M')
            else:
                growth_goals_data["date"] = growth_goals_date
            growth_goals_data['order_time'] = datetime.combine(order_time, datetime.min.time())
            growth_goals_data['start_date'] = datetime.combine(start_date, datetime.min.time())
        else:
            continue

        growth_goals_data['is_complete'] = True if (growth_goals_module.growth_goals or
                                                    datetime.now().date() >= growth_goals_data['start_date'].date()) else False

        if growth_goals_module.growth_goals:
            growth_goals_data['status'] = 3  # 已解锁 已设置目标
        elif growth_goals_data['start_date'].date() - timedelta(days=3) >= datetime.now().date():
            growth_goals_data['status'] = 1  # 未解锁
        else:
            if role:
                growth_goals_data['status'] = 2  # 任务已解锁 不需要区分客户是否设置目标，教练就是查看详情，点击后进入客户的目标详情页面
            else:
                if not growth_goals_module.growth_goals:
                    growth_goals_data['status'] = 2  # 已解锁 未设置目标
                else:
                    growth_goals_data['status'] = 1
        data.append(growth_goals_data)
    return data


def get_member_service_workshop_data(workshop_ids, role, project_member):
    """
    获取项目成员服务的工作坊数据
    order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
    :param workshop_ids: 工作坊id列表
    :param role: 角色 教练请求时传入
    :param project_member: 项目成员
    """
    data = []
    for group_coach in GroupCoach.objects.filter(id__in=workshop_ids, deleted=False):
        group_coach_data = {"data_type": DataType.group_coach, "module_id": group_coach.id,
                            "name": f"工作坊：{group_coach.project_group_coach.theme}"}
        interview = group_coach.interview
        group_coach_data['date'] = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                   f"{interview.public_attr.end_time.strftime('%H:%M')}"
        group_coach_data['interview_id'] = interview.id
        group_coach_data['order_time'] = interview.public_attr.start_time
        group_coach_data['is_complete'] = True if datetime.now().date() >= group_coach_data[
            'order_time'].date() else False

        if group_coach_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
            group_coach_data['status'] = 1  # 未解锁
        elif role:
            group_coach_data['status'] = 2  # 已解锁
        elif group_coach_data['order_time'] - timedelta(minutes=30) > datetime.now():
            group_coach_data['status'] = 2  # 已解锁 且 当前时间小于开始时间-30分钟
        elif group_coach_data['order_time'] - timedelta(
                minutes=30) <= datetime.now() and not interview.coachee_record_status:
            group_coach_data['status'] = 3  # 且 当前时间大于等于开始时间-30分钟  且 未填写记录
        elif interview.coachee_record_status:
            group_coach_data['status'] = 4  # 已解锁 且 已填写记录
        data.append(group_coach_data)

    return data


def get_member_service_group_tutoring_data(group_tutoring_ids, role, project_member):
    """
        获取项目成员服务的工作坊数据
        order_time是在当前阶段中综合所有类型数据后用来排序的时间，同一阶段不同数据类型取值不一样，使用中仅做排序使用
        :param workshop_ids: 工作坊id列表
        :param role: 角色 教练请求时传入
        :param project_member: 项目成员
        """
    data = []
    for group_coach in GroupCoach.objects.filter(id__in=group_tutoring_ids, deleted=False):
        group_coach_data = {"data_type": DataType.group_tutoring, "module_id": group_coach.id,
                            "name": f"小组辅导：{group_coach.project_group_coach.theme}"}
        interview = group_coach.interview
        group_coach_data['date'] = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                   f"{interview.public_attr.end_time.strftime('%H:%M')}"
        group_coach_data['interview_id'] = interview.id
        group_coach_data['order_time'] = interview.public_attr.start_time
        group_coach_data['is_complete'] = True if datetime.now().date() >= group_coach_data[
            'order_time'].date() else False

        if group_coach_data['order_time'].date() - timedelta(days=3) >= datetime.now().date():
            group_coach_data['status'] = 1  # 未解锁
        elif role:
            group_coach_data['status'] = 2  # 已解锁
        elif group_coach_data['order_time'] - timedelta(minutes=30) > datetime.now():
            group_coach_data['status'] = 2  # 已解锁 且 当前时间小于开始时间-30分钟
        elif group_coach_data['order_time'] - timedelta(
                minutes=30) <= datetime.now() and not interview.coachee_record_status:
            group_coach_data['status'] = 3  # 且 当前时间大于等于开始时间-30分钟  且 未填写记录
        elif interview.coachee_record_status:
            group_coach_data['status'] = 4  # 已解锁 且 已填写记录
        data.append(group_coach_data)
    return data


def content_data_order(content_data):
    """
    对内容数据进行排序
    :param content_data: 内容数据
    """
    content = content_data['content']
    if content:
        sorted_content = sorted(content, key=lambda x: x['order_time'], reverse=False)
        start_order_time = sorted_content[0]['order_time']
        end_order_time = sorted_content[-1]['order_time']
        if datetime.now() < start_order_time:
            content_data['status'] = 1  # 未开始
        elif start_order_time <= datetime.now() <= end_order_time:
            content_data['status'] = 2  # 进行中
        elif datetime.now() > end_order_time:
            content_data['status'] = 3  # 已完成
        content_data['content'] = sorted_content
    return content_data


def get_member_service_interview_date(project_member, hours):
    """
    获取项目成员服务的辅导x小时后配置模块的建议时间，排序时间，开始日期，
    :param project_member: 项目成员
    :param hours: 辅导x小时
    """
    module_date = None
    order_time = None
    start_date = None

    interview_contents = ProjectMemberServiceContent.objects.filter(
        project_member=project_member, deleted=False,
        content_type=DataType.interview, object_ids__isnull=False
    )
    if not interview_contents.exists():
        return module_date, order_time, start_date
    one_to_one_interview_ids = []
    for interview_content in interview_contents:
        one_to_one_interview_ids.extend(interview_content.object_ids)
    one_to_one_interviews = OneToOneCoach.objects.filter(id__in=one_to_one_interview_ids,
                                                         deleted=False).order_by('suggested_start_date')

    if not one_to_one_interviews.exists():
        return module_date, order_time, start_date
    interview_list = split_member_service_interview_data(project_member, one_to_one_interviews)

    # if stage_id:
    #     contents = interview_contents.filter(service_stage_id=stage_id)
    #     one_to_one_ids = []
    #     for c in contents:
    #         one_to_one_ids.extend(c.object_ids)
    #     one_to_one = OneToOneCoach.objects.filter(id__in=one_to_one_ids, deleted=False).order_by('suggested_start_date')
    #
    #     tmp_list = [{"id": i.id, "count": i.online_available_time / i.suggested_duration}
    #                 for i in one_to_one_interviews]
    #     index_list = []  # 获取数量切片
    #     for o in one_to_one:
    #         exists_count = 0
    #         for index, tmp in enumerate(tmp_list):
    #             if o.id == tmp['id']:
    #                 index_list.append([exists_count, exists_count + tmp['count']])
    #             exists_count += tmp['count']
    #     if index_list:
    #         start = index_list[0][0]
    #         end = index_list[-1][-1]
    #         if start and end:
    #             interview_list = interview_list[start - 1:end]

    tmp_hours = 0
    for interview in interview_list:
        # 辅导时间0小时后展示的特殊处理，卡片展示在“第一次辅导”上方，建议开始时间为第一次辅导开始时间+三天
        if hours == 0:
            module_date = f"建议：{(interview['complete_date'] - timedelta(days=10)).strftime('%Y-%m-%d')}~" \
                          f"{(interview['complete_date'] - timedelta(days=7)).strftime('%Y-%m-%d')}"
            order_time = interview['order_time'] - timedelta(days=1)
            start_date = datetime.now().date()
            break
        tmp_hours += interview['hour']
        if tmp_hours >= hours:  # 满足时间
            module_date = f"建议：{(interview['complete_date'] + timedelta(days=1)).strftime('%Y-%m-%d')}~" \
                          f"{(interview['complete_date'] + timedelta(days=4)).strftime('%Y-%m-%d')}"
            order_time = interview['order_time'] + timedelta(days=1)
            start_date = interview['complete_date'] + timedelta(days=1)
            break
    return module_date, order_time, start_date


def split_member_service_interview_data(project_member, one_to_one_interviews, coach_user_id=None):
    """
    切分项目成员服务的辅导数据
    :param project_member: 项目成员
    :param one_to_one_interviews: 项目成员服务的辅导数据
    :param coach_user_id: 教练用户标识

    change_observation：改变观察反馈
    growth_goals：成长目标
    coach_task：教练任务
    三种类型数据可以设置辅导x小时后开启，因此和辅导卡片排序时间/开始时间数据深度绑定
    order_time逻辑变动或complete_date逻辑变动需要考虑上面三种数据的逻辑变动
    """
    if data_redis.get(f"project_member_{project_member.pk}"):
        return pickle.loads(data_redis.get(f"project_member_{project_member.pk}"))

    interview_list = []
    interviews = ProjectInterview.objects.filter(
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
        public_attr__type=ATTR_TYPE_INTERVIEW,
        type=ProjectInterviewTypeEnum.formal_interview, public_attr__project=project_member.project, deleted=False,
        public_attr__target_user=project_member.user,
        order__isnull=True
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')

    if coach_user_id:
        interviews = interviews.filter(public_attr__user_id=coach_user_id)
    if interviews.exists():
        one_to_one_count_list = []
        all_order_suggested_start_date = []
        for one_to_one_interview in one_to_one_interviews:
            count = math.ceil(one_to_one_interview.online_available_time / one_to_one_interview.suggested_duration)
            one_to_one_count_list.append(count)
            for i in range(count):
                interval_start = timedelta(days=one_to_one_interview.suggested_interval * 7 * i)
                all_order_suggested_start_date.append(
                    datetime.combine(one_to_one_interview.suggested_start_date + interval_start, datetime.min.time()))

        tmp_count = copy.deepcopy(one_to_one_count_list)
        t_index = 0

        observation_count = 0
        for index, interview in enumerate(list(interviews)):
            data = {'data_type': DataType.interview, 'module_id': None, 'interview_id': interview.id,
                    'interview_subject': interview.interview_subject, 'place_type': interview.place_type}
            # 影子观察, 多方会谈不计算辅导次数。
            if interview.interview_subject in [
                InterviewSubjectEnum.shadow_observation.value, InterviewSubjectEnum.multi_party.value]:
                data['name'] = InterviewSubjectEnum.get_display(interview.interview_subject)
                observation_count += 1
            else:
                data['name'] = f"第{len(interview_list) + 1 - observation_count}次：教练辅导"

            data['date'] = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                           f"{interview.public_attr.end_time.strftime('%H:%M')}"
            data['start_time'] = interview.public_attr.start_time
            data['order_time'] = all_order_suggested_start_date[index] if index < len(all_order_suggested_start_date) else interview.public_attr.start_time
            data['complete_date'] = interview.public_attr.start_time.date()
            data['is_complete'] = True if datetime.now().date() >= data['complete_date'] else False
            data['hour'] = round(int(interview.times) / 60, 1)
            data['record_type'] = interview.record_type
            data['meeting_info'] = interview_public.get_interview_meeting_info(interview)
            interview_list.append(data)
            if t_index + 1 <= len(tmp_count):  # 当前总次数未被用尽
                if tmp_count[t_index] > 0:  # 当前一对一模块次数未用尽
                    tmp_count[t_index] -= 1  # 减去次数
                else:
                    t_index += 1  # 切换至下一个一对一辅导
                    if t_index < len(tmp_count):
                        tmp_count[t_index] -= 1

        for index, one_to_one_interview in enumerate(one_to_one_interviews):
            if tmp_count[index] != 0:  # 当前阶段一对一辅导次数未用尽
                if tmp_count[index] != one_to_one_count_list[index]:  # 次数被用过
                    used_count = (one_to_one_count_list[index] - tmp_count[index])  # 总次数-剩余次数=已用次数
                    range_count = tmp_count[index]
                else:  # 次数未被用过
                    used_count = 0
                    range_count = one_to_one_count_list[index]
                for i in range(range_count):
                    data = {'data_type': DataType.interview, 'module_id': one_to_one_interview.id,
                            'interview_id': None, 'interview_subject': None, 'place_type': None}
                    interval_start = timedelta(days=one_to_one_interview.suggested_interval * 7 * (i + used_count))
                    interval_end = timedelta(days=one_to_one_interview.suggested_interval * 7 * (i + 1 + used_count) - 1)
                    data['date'] = \
                        f"建议：{(one_to_one_interview.suggested_start_date + interval_start).strftime('%Y-%m-%d')}~" \
                        f"{(one_to_one_interview.suggested_start_date + interval_end).strftime('%Y-%m-%d')}"
                    data['complete_date'] = one_to_one_interview.suggested_start_date + interval_end
                    data['start_time'] = datetime.combine(one_to_one_interview.suggested_start_date + interval_start, datetime.min.time())
                    data['order_time'] = datetime.combine(one_to_one_interview.suggested_start_date + interval_start, datetime.min.time())
                    data['name'] = f"第{len(interview_list) + 1 - observation_count}次：教练辅导"  # 需要减去影子观察次数
                    data['is_complete'] = True if datetime.now().date() > data['complete_date'] else False
                    data['hour'] = one_to_one_interview.suggested_duration
                    data['record_type'] = InterviewRecordTypeEnum.questionnaire.value
                    interview_list.append(data)

    else:
        for one_to_one_interview in one_to_one_interviews:
            count = math.ceil(one_to_one_interview.online_available_time / one_to_one_interview.suggested_duration)
            for i in range(count):
                data = {'data_type': DataType.interview, 'module_id': one_to_one_interview.id, 'interview_id': None,
                        'interview_subject': None, 'place_type': None}
                interval_start = timedelta(days=one_to_one_interview.suggested_interval * 7 * i)
                interval_end = timedelta(days=one_to_one_interview.suggested_interval * 7 * (i + 1) - 1)
                data['date'] = \
                    f"建议：{(one_to_one_interview.suggested_start_date + interval_start).strftime('%Y-%m-%d')}~" \
                    f"{(one_to_one_interview.suggested_start_date + interval_end).strftime('%Y-%m-%d')}"
                data['start_time'] = datetime.combine(one_to_one_interview.suggested_start_date + interval_start, datetime.min.time())
                data['complete_date'] = one_to_one_interview.suggested_start_date + interval_end
                data['order_time'] = datetime.combine(one_to_one_interview.suggested_start_date + interval_start, datetime.min.time())
                data['name'] = f"第{len(interview_list) + 1}次：教练辅导"
                data['is_complete'] = True if datetime.now().date() > data['complete_date'] else False
                data['hour'] = one_to_one_interview.suggested_duration
                data['record_type'] = InterviewRecordTypeEnum.questionnaire.value
                interview_list.append(data)

    data_redis.set(f"project_member_{project_member.pk}", pickle.dumps(interview_list), ex=30)
    return interview_list


def get_project_service_article_content(content, project_service_content):
    """
    项目详情-项目路径图文章数据
    :param content: 项目路径图文章数据
    :param project_service_content: 项目服务内容
    """
    data = []
    exists_date = []
    for article in content:
        article_data = {
            "data_type": DataType.article,
            "start_date": article['start_date'],
            "end_date": article['end_date'],
            "article_id": article['article_id'],
            "name": "课前阅读", "users": [],
            "date": f"{article['start_date']}~{article['end_date']}",
            "order_time": datetime.strptime(article['start_date'], '%Y-%m-%d')}
        start_date = datetime.strptime(article['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(article['end_date'], '%Y-%m-%d').date()
        if datetime.now().date() < start_date:
            article_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            article_data['status'] = 2  # 进行中
        else:
            article_data['status'] = 3  # 已结束
        if article_data['date'] not in exists_date:
            data.append(article_data)
            exists_date.append(article_data['date'])
    return data


# 获取项目详情-项目路径图测评数据
def get_project_service_evaluation_content(content, project_service_content):
    """
    项目详情-项目路径图测评数据
    :param content: 项目路径图测评数据
    :param project_service_content: 项目服务内容
    """
    data = []
    for evaluation in content:
        evaluation_data = {"data_type": DataType.evaluation, "id": evaluation.get('evaluation_id'),
                           "name": get_project_service_content(project_service_content, evaluation).get('name'),
                           "users": [],
                           "date": f"{evaluation['start_date']}~{evaluation['end_date']}",
                           "order_time": datetime.strptime(evaluation['start_date'], '%Y-%m-%d')}
        start_date = datetime.strptime(evaluation['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(evaluation['end_date'], '%Y-%m-%d').date()
        if datetime.now().date() < start_date:
            evaluation_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            evaluation_data['status'] = 2  # 进行中
        else:
            evaluation_data['status'] = 3  # 已结束
        data.append(evaluation_data)
    return data


def get_project_service_chemical_interview_content(content):
    """
    项目详情-项目路径图化学面谈数据
    :param content: 项目路径图化学面谈数据
    """
    data = []
    for chemical_interview in content:
        chemical_interview_data = {"name": "化学面谈", "data_type": DataType.chemical_interview,
                                   "date": f"{chemical_interview['start_date']}~{chemical_interview['end_date']}",
                                   "order_time": datetime.strptime(chemical_interview['start_date'], '%Y-%m-%d'),
                                   "users": []}
        start_date = datetime.strptime(chemical_interview['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(chemical_interview['end_date'], '%Y-%m-%d').date()
        if datetime.now().date() < start_date:
            chemical_interview_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            chemical_interview_data['status'] = 2  # 进行中
        else:
            chemical_interview_data['status'] = 3  # 已结束
        data.append(chemical_interview_data)
    return data


def get_project_service_stakeholder_interview_content(content):
    """
    项目详情-项目路径图利益相关者访谈数据
    :param content: 项目路径图利益相关者访谈数据
    """
    data = []
    for stakeholder_interview in content:
        stakeholder_interview_data = {
            "name": "利益相关者访谈", "data_type": DataType.stakeholder_interview, "users": [],
            "date": f"{stakeholder_interview['start_date']}~{stakeholder_interview['end_date']}",
            "order_time": datetime.strptime(stakeholder_interview['start_date'], '%Y-%m-%d'),
            "duration": stakeholder_interview['duration'],
            "coach_template_id": stakeholder_interview['coach_template_id'],
            "report_template_id": stakeholder_interview['report_template_id'],
            "stakeholder_interview_number": stakeholder_interview['stakeholder_interview_number']
        }
        start_date = datetime.strptime(stakeholder_interview['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(stakeholder_interview['end_date'], '%Y-%m-%d').date()
        stakeholder_interview_data['start_date'] = start_date
        stakeholder_interview_data['end_date'] = end_date
        if datetime.now().date() < start_date:
            stakeholder_interview_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            stakeholder_interview_data['status'] = 2  # 进行中
        else:
            stakeholder_interview_data['status'] = 3  # 已结束
        data.append(stakeholder_interview_data)
    return data


# 获取项目详情-项目路径图教练任务数据
def get_project_service_coach_task_content(content, project, project_service_content):
    """
    项目详情-项目路径图教练任务数据
    :param content: 项目路径图教练任务数据
    :param project: 项目
    :param project_service_content: 项目服务内容
    """
    data = []
    for coach_task in content:
        coach_task_data = {"data_type": DataType.coach_tasks, "hours": coach_task['hours'],
                           "template_id": coach_task["template_id"], "users": [],
                           "name": get_project_service_content(project_service_content, coach_task).get(
                               'template_name')}
        coach_task_date, start_date = get_project_service_interview_date(coach_task['hours'], project)
        if not coach_task_date or not start_date:
            continue
        end_date = start_date + timedelta(days=3)
        if datetime.now().date() < start_date:
            coach_task_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            coach_task_data['status'] = 2  # 进行中
        else:
            coach_task_data['status'] = 3  # 已结束
        coach_task_data['date'] = coach_task_date
        coach_task_data['order_time'] = datetime.combine(start_date, datetime.min.time())
        data.append(coach_task_data)
    return data


def get_project_service_one_to_one_interview_content(project, stage_id):
    """
    项目详情-项目路径图一对一辅导数据
    :param project: 项目
    :param stage_id: 阶段id
    """
    interview_list = split_project_service_interview(project)
    if stage_id:
        interview_list = split_project_service_stage_interview(project, interview_list, stage_id)
    return interview_list


def get_project_service_growth_target_content(content, project):
    """
    项目详情-项目路径图成长目标数据
    :param content: 项目路径图成长目标数据
    :param project: 项目
    """
    data = []
    for growth_goal in content:
        growth_goal_data = {"data_type": DataType.growth_goals, "name": "成长目标", "users": [],
                            "type": growth_goal['type']}
        if growth_goal['type'] == GrowthGoalsModelTypeEnum.fixed:
            growth_goal_data['date'] = f"{growth_goal['start_date']}~{growth_goal['end_date']}"
            growth_goal_data['order_time'] = datetime.strptime(growth_goal['start_date'], '%Y-%m-%d')
            growth_goal_data['start_date'] = growth_goal['start_date']
            growth_goal_data['end_date'] = growth_goal['start_date']
        else:
            growth_goal_date, start_date = get_project_service_interview_date(growth_goal['hours'], project)
            if not growth_goal_date or not start_date:
                continue
            growth_goal_data['hours'] = growth_goal['hours']
            end_date = start_date + timedelta(days=3)
            if datetime.now().date() < start_date:
                growth_goal_data['status'] = 1  # 未开始
            elif start_date <= datetime.now().date() <= end_date:
                growth_goal_data['status'] = 2  # 进行中
            else:
                growth_goal_data['status'] = 3  # 已结束
            growth_goal_data['date'] = growth_goal_date
            growth_goal_data['order_time'] = datetime.combine(start_date, datetime.min.time())
        data.append(growth_goal_data)
    return data


def get_project_service_workshop_content(content, project_service_content):
    """
    项目详情-项目路径图工作坊数据
    :param content: 项目路径图工作坊数据
    :param project_service_content: 项目服务内容
    """
    data = []
    for workshop in content:
        workshop_data = {
            "name": f"工作坊：{workshop['theme']}",
            "data_type": DataType.group_coach, "users": [], "theme": workshop['theme'],
            "course_date": workshop['course_date'], "template_id": workshop['template_id'],
            "course_place": workshop['course_place'],
            "start_course_time": workshop['start_course_time'], "end_course_time": workshop['end_course_time'],
            "date": f"{workshop['course_date']} {workshop['start_course_time'][:5]}-{workshop['end_course_time'][:5]}",
            "order_time": datetime.strptime(f"{workshop['course_date']} {workshop['start_course_time'][:5]}",
                                            '%Y-%m-%d %H:%M')}
        start_date = datetime.strptime(workshop['course_date'], '%Y-%m-%d').date()
        if datetime.now().date() < start_date:
            workshop_data['status'] = 1  # 未开始
        elif datetime.now().date() == start_date:
            workshop_data['status'] = 2  # 进行中
        else:
            workshop_data['status'] = 3  # 已结束
        data.append(workshop_data)
    return data


def get_project_service_group_tutoring_content(content, project_service_content):
    """
       项目详情-项目路径图小组辅导数据
       :param content: 项目路径图小组辅导数据
       :param project_service_content: 项目服务内容
       """
    data = []
    for workshop in content:
        workshop_data = {
            "name": f"小组辅导：{workshop['theme']}",
            "data_type": DataType.group_tutoring, "users": [], "theme": workshop['theme'],
            "course_date": workshop['course_date'], "template_id": workshop['template_id'],
            "course_place": workshop['course_place'],
            "start_course_time": workshop['start_course_time'], "end_course_time": workshop['end_course_time'],
            "date": f"{workshop['course_date']} {workshop['start_course_time'][:5]}-{workshop['end_course_time'][:5]}",
            "order_time": datetime.strptime(f"{workshop['course_date']} {workshop['start_course_time'][:5]}",
                                            '%Y-%m-%d %H:%M')}
        start_date = datetime.strptime(workshop['course_date'], '%Y-%m-%d').date()
        if datetime.now().date() < start_date:
            workshop_data['status'] = 1  # 未开始
        elif datetime.now().date() == start_date:
            workshop_data['status'] = 2  # 进行中
        else:
            workshop_data['status'] = 3  # 已结束
        data.append(workshop_data)
    return data


def get_project_service_change_observation_content(content, project):
    """
    项目详情-项目路径图改变观察调研数据
    :param content: 项目路径图改变观察调研数据
    :param project: 项目
    """
    data = []
    for change_observation in content:
        change_observation_data = {"data_type": DataType.change_observation, "name": "改变观察调研", "users": [],
                                   "write_condition": change_observation['write_condition'],
                                   "remind_type": change_observation['remind_type']}
        change_observation_date, start_date = get_project_service_interview_date(
            change_observation['write_condition'], project)
        # 如果配置的辅导时长不够开启改变观察反馈，则在项目路径图中不展示
        if not change_observation_date or not start_date:
            continue
        end_date = start_date + timedelta(days=3)
        if datetime.now().date() < start_date:
            change_observation_data['status'] = 1  # 未开始
        elif start_date <= datetime.now().date() <= end_date:
            change_observation_data['status'] = 2  # 进行中
        else:
            change_observation_data['status'] = 3  # 已结束

        change_observation_data['date'] = change_observation_date
        change_observation_data['order_time'] = datetime.combine(start_date, datetime.min.time())
        data.append(change_observation_data)
    return data


def get_project_service_interview_date(hours, project):
    """
    根据辅导获取项目服务内容中教练任务、成长目标等日期
    :param hours: 辅导时长
    :param project: 项目
    """
    module_date = None
    interview_list = split_project_service_interview(project)
    tmp_hours = 0
    start_date = None
    for interview in interview_list:
        if hours == 0:
            module_date = f"建议：{(interview['complete_date'] - timedelta(days=10)).strftime('%Y-%m-%d')}~" \
                          f"{(interview['complete_date'] - timedelta(days=7)).strftime('%Y-%m-%d')}"
            start_date = interview['complete_date'] - timedelta(days=1)


        tmp_hours += interview['hour']
        if tmp_hours >= hours:  # 满足时间
            module_date = f"建议：{(interview['complete_date'] + timedelta(days=1)).strftime('%Y-%m-%d')}~" \
                          f"{(interview['complete_date'] + timedelta(days=4)).strftime('%Y-%m-%d')}"
            start_date = interview['complete_date'] + timedelta(days=1)
            break
    return module_date, start_date


def split_project_service_stage_interview(project, interview_list, stage_id):
    """
    分阶段根据阶段截取当前阶段的辅导
    :param project: 项目
    :param interview_list: 辅导列表
    :param stage_id: 阶段id
    """
    if data_redis.get(f"project_service_stage_{project.pk}-{stage_id}"):
        return pickle.loads(data_redis.get(f"project_service_stage_{project.pk}-{stage_id}"))
    # 查出所有辅导配置并按时间排序
    interview_contents = ProjectServiceContent.objects.filter(deleted=False, project=project, content__isnull=False,
                                                              content_type=DataType.interview)
    interview_service_list = []
    for interview_content in interview_contents:
        content = interview_content.content
        for index, c in enumerate(content):
            c['id'] = f"{interview_content.id}-{index}"
        interview_service_list.extend(content)
    # 案件已开始日期从小到大排序
    interview_service_list = sorted(interview_service_list, key=lambda x: datetime.strptime(x['suggested_start_date'],
                                                                                            '%Y-%m-%d'))

    # 查询出当前阶段的辅导配置按开始日期排序
    stage_interview_content = interview_contents.filter(service_stage_id=stage_id)
    tmp_interview_service_list = []
    for tmp_interview_content in stage_interview_content:
        content = tmp_interview_content.content
        for index, c in enumerate(content):
            c['id'] = f"{tmp_interview_content.id}-{index}"
        tmp_interview_service_list.extend(content)
    # 案件已开始日期从小到大排序
    tmp_interview_service_list = sorted(tmp_interview_service_list,
                                        key=lambda x: datetime.strptime(x['suggested_start_date'], '%Y-%m-%d'))
    index_list = []  # 获取数量切片
    for tmp_service in tmp_interview_service_list:
        exists_count = 0
        for index, tmp in enumerate(interview_service_list):
            if tmp_service['id'] == tmp['id']:
                index_list.append([exists_count, exists_count + math.ceil(tmp['hours'] / tmp['suggested_duration'])])
            exists_count += math.ceil(tmp['hours'] / tmp['suggested_duration'])
    if index_list:
        start = index_list[0][0]
        end = index_list[-1][-1]
        interview_list = interview_list[start:end]
    data_redis.set(f"project_service_stage_{project.pk}-{stage_id}", pickle.dumps(interview_list), ex=30)
    return interview_list


def split_project_service_interview(project):
    """
    切分项目服务内容的一对一辅导
    :param project: 项目
    """
    if data_redis.get(f"project_service_{project.pk}"):
        return pickle.loads(data_redis.get(f"project_service_{project.pk}"))
    # 取出项目下所有的一对一辅导配置 写入新的一对一辅导配置列表
    interview_contents = ProjectServiceContent.objects.filter(deleted=False, project=project, content__isnull=False,
                                                              content_type=DataType.interview)
    interview_service_list = []
    for interview_content in interview_contents:
        content = interview_content.content
        for c in content:
            c['id'] = interview_content.id
        interview_service_list.extend(content)
    # 案件已开始日期从小到大排序
    interview_service_list = sorted(interview_service_list, key=lambda x: datetime.strptime(x['suggested_start_date'],
                                                                                            '%Y-%m-%d'))
    interview_list = []
    for interview_service in interview_service_list:
        count = math.ceil(interview_service['hours'] / interview_service['suggested_duration'])
        for i in range(count):
            # 按照建议辅导间隔，设置每个辅导的开始和结束日期
            interval_start = timedelta(days=interview_service['suggested_interval'] * 7 * i)
            interval_end = timedelta(days=interview_service['suggested_interval'] * 7 * (i+1) - 1)
            interview_data = {"data_type": DataType.interview, "name": f"第{len(interview_list) + 1}次：教练辅导",
                              "users": []}
            interview_data['date'] = \
                f"建议：{(datetime.strptime(interview_service['suggested_start_date'], '%Y-%m-%d').date() + interval_start).strftime('%Y-%m-%d')}~" \
                f"{(datetime.strptime(interview_service['suggested_start_date'], '%Y-%m-%d').date() + interval_end).strftime('%Y-%m-%d')}"
            interview_data['complete_date'] = datetime.strptime(interview_service['suggested_start_date'],
                                                                '%Y-%m-%d').date() + interval_end

            interview_data['order_time'] = datetime.combine(
                (datetime.strptime(interview_service['suggested_start_date'], '%Y-%m-%d').date() + interval_start), datetime.min.time())
            interview_data['name'] = f"第{len(interview_list) + 1}次：教练辅导"
            interview_data['is_complete'] = True if datetime.now().date() >= interview_data['complete_date'] else False
            interview_data['hour'] = interview_service['suggested_duration']
            interview_data['count'] = len(interview_list) + 1
            if datetime.now().date() < interview_data['complete_date']:
                interview_data['status'] = 1
            elif datetime.now().date() == interview_data['complete_date']:
                interview_data['status'] = 2
            else:
                interview_data['status'] = 3
            interview_list.append(interview_data)
    data_redis.set(f"project_service_{project.pk}", pickle.dumps(interview_list), ex=30)
    return interview_list


def get_project_path_diagram(project, coach=None):
    """
    获取项目路径图详情
    :param project: 项目
    :param coach: 教练
    """
    project_stages = ProjectServiceStage.objects.filter(project=project, deleted=False)
    if not project_stages:  # 服务阶段为空，不分阶段
        data = {'is_stage': False, 'stage_data': []}
        project_content = ProjectServiceContent.objects.filter(project=project, deleted=False)
        if not project_content.exists():
            return None
        stage_names = [None]
        project_stage = [list(project_content)]
        stage_ids = []
    else:
        data = {'is_stage': True, 'stage_data': []}
        stage_ids = list(project_stages.order_by('order').values_list('id', flat=True))
        stage_names = list(project_stages.order_by('order').values_list('stage_name', flat=True))

        project_stage = []
        for item in stage_ids:
            project_content = ProjectServiceContent.objects.filter(project=project, service_stage_id=item,
                                                                   deleted=False)
            project_stage.append(list(project_content))

    for index, content_list in enumerate(project_stage):
        stage_id = None
        stage_name = None if not data['is_stage'] else stage_names[index]
        if data['is_stage'] and stage_ids:
            stage_id = stage_ids[index]
        content_data = {"name": stage_name, "status": 1, "content": []}
        for stage_content in content_list:  # stage_content = ProjectServiceContentOBJ
            if stage_content.content:
                if stage_content.content_type == DataType.article:
                    article_data = get_project_service_article_content(stage_content.content, stage_content)
                    content_data['content'].extend(article_data)
                elif stage_content.content_type == DataType.evaluation:
                    evaluation_data = get_project_service_evaluation_content(stage_content.content, stage_content)
                    content_data['content'].extend(evaluation_data)
                elif stage_content.content_type == DataType.chemical_interview:
                    chemical_interview_data = get_project_service_chemical_interview_content(stage_content.content)
                    content_data['content'].extend(chemical_interview_data)
                elif stage_content.content_type == DataType.stakeholder_interview:
                    stakeholder_interview_data = get_project_service_stakeholder_interview_content(
                        stage_content.content)
                    content_data['content'].extend(stakeholder_interview_data)
                elif stage_content.content_type == DataType.coach_tasks:
                    coach_task_data = get_project_service_coach_task_content(stage_content.content, project,
                                                                             stage_content)
                    content_data['content'].extend(coach_task_data)
                elif stage_content.content_type == DataType.interview:
                    one_to_one_interview_data = get_project_service_one_to_one_interview_content(project, stage_id)
                    one_to_one_interview_data = [interview_data for interview_data in one_to_one_interview_data
                                                 if interview_data not in content_data['content']]
                    content_data['content'].extend(one_to_one_interview_data)
                elif stage_content.content_type == DataType.growth_goals:
                    growth_target_data = get_project_service_growth_target_content(stage_content.content, project)
                    content_data['content'].extend(growth_target_data)
                elif stage_content.content_type == DataType.group_coach:
                    workshop_data = get_project_service_workshop_content(stage_content.content, stage_content)
                    content_data['content'].extend(workshop_data)
                elif stage_content.content_type == DataType.group_tutoring:
                    group_tutoring_data = get_project_service_group_tutoring_content(stage_content.content, stage_content)
                    content_data['content'].extend(group_tutoring_data)
                elif stage_content.content_type == DataType.change_observation:
                    change_observation_data = get_project_service_change_observation_content(stage_content.content,
                                                                                             project)
                    content_data['content'].extend(change_observation_data)
        content_data = content_data_order(content_data)
        data['stage_data'].append(content_data)
    return data


def get_project_path_member(project, coach, is_5alc):
    project_data = get_project_path_diagram(project)
    if not project_data:
        return None
    project_member_list = []
    if is_5alc:
        member_ids = list(ProjectCoach.objects.filter(
            coach=coach, project=project, member__isnull=False, project_group_coach__isnull=True,
            deleted=False).values_list('member_id', flat=True))
        if not member_ids:
            return project_data
        project_members = ProjectMember.objects.filter(user_id__in=member_ids, project=project, deleted=False)
        if not project_members.exists():
            return project_data
        project_is_stage = ProjectServiceStage.objects.filter(project=project, deleted=False).exists()
        for project_member in project_members:
            if project_member.is_stage != project_is_stage:
                continue  # 用户与项目配置服务内容冲突，跳过
            if ProjectServiceStage.objects.filter(project_member=project_member, deleted=False).exists() and \
                    project_member.is_stage:  # 用户分阶段但是使用的自定义的阶段，跳过
                continue
            if not project_member.is_stage and not ProjectMemberServiceContent.objects.filter(
                    project_service_members__project_service__project_id=project.id,
                    project_service_members__deleted=False).exists():  # 用户和项目不分阶段，跳过
                continue
            project_member_list.append(project_member)
    for stage_data in project_data['stage_data']:  # 循环每个阶段
        if not project_data['stage_data']:
            continue
        for data in stage_data['content']:  # 循环每个阶段下的内容
            data['total_count'] = None
            data['completed_count'] = None
            if data['data_type'] == DataType.evaluation:
                evaluation = Evaluation.objects.filter(id=data['id'], deleted=False).first()
                all_evaluation_count = EvaluationModule.objects.filter(
                    evaluation=evaluation, project_bundle__project=project, deleted=False).count()
                exists_evaluation_count = PublicAttr.objects.filter(
                    project=project, type=ATTR_TYPE_EVALUATION_ANSWER, user__isnull=False,
                    target_user__isnull=True).count() if evaluation.code == MANAGE_EVALUATION else \
                    PublicAttr.objects.filter(project=project, type=ATTR_TYPE_EVALUATION_ANSWER,
                                              target_user=F('user')).count()

                if is_5alc:
                    for project_member in project_member_list:
                        if EvaluationModule.objects.filter(evaluation=evaluation,
                                                           project_bundle__project_member=project_member,
                                                           deleted=False).exists():
                            public_attr = PublicAttr.objects.filter(
                                project=project, user=project_member.user, target_user__isnull=True,
                                type=ATTR_TYPE_EVALUATION_ANSWER) if evaluation.code == MANAGE_EVALUATION else \
                                PublicAttr.objects.filter(
                                    project=project, user=project_member.user, target_user=project_member.user,
                                    type=ATTR_TYPE_EVALUATION_ANSWER)
                            if public_attr.exists():
                                data['users'].append({"user_id": project_member.user_id,
                                                      "true_name": project_member.user.cover_name,
                                                      "head_image_url": project_member.user.head_image_url})
                else:
                    if evaluation.code == MANAGE_EVALUATION:  # 教练型管理者测评
                        data['total_count'] = all_evaluation_count
                        data['completed_count'] = exists_evaluation_count

                if data['order_time'] > datetime.now():  # 开始时间大于当前时间
                    data['status'] = 1  # 未开始
                elif exists_evaluation_count == all_evaluation_count and all_evaluation_count != 0:
                    data['status'] = 3  # 已完成
                else:
                    data['status'] = 2  # 进行中

            elif data['data_type'] == DataType.group_coach:
                start_course_time = datetime.strptime(f"{data['course_date']} {data['start_course_time']}",
                                                      "%Y-%m-%d %H:%M")
                end_course_time = datetime.strptime(f"{data['course_date']} {data['end_course_time']}",
                                                    "%Y-%m-%d %H:%M")
                all_group_coach = GroupCoach.objects.filter(
                    project_bundle__project=project, project_group_coach__theme=data['theme'],
                    project_group_coach__start_time=start_course_time,
                    project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                    project_group_coach__end_time=end_course_time, project_group_coach__place=data['course_place'],
                    inter_view_record_template_id=data['template_id'], deleted=False)
                if is_5alc:
                    for project_member in project_member_list:
                        if GroupCoach.objects.filter(
                                project_bundle__project_member=project_member,
                                project_group_coach__theme=data['theme'],
                                project_group_coach__place=data['course_place'],
                                project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                                interview__coachee_record_status=True).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})
                data['total_count'] = all_group_coach.count()

                project_interview = ProjectInterview.objects.filter(
                    Q(coach_record_status=True) | Q(coachee_record_status=True),
                    topic=data['theme'], place_category=3, place=data['course_place'], type=1,
                    public_attr__type=1, public_attr__project=project,
                    public_attr__start_time=start_course_time,
                    public_attr__end_time=end_course_time,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                    deleted=False, interview_subject=5).distinct()

                data['completed_count'] = project_interview.count()

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_group_coach.count() == project_interview.filter(
                        public_attr__end_time__lt=datetime.now()).count() and all_group_coach.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.group_tutoring:
                start_course_time = datetime.strptime(f"{data['course_date']} {data['start_course_time']}",
                                                      "%Y-%m-%d %H:%M")
                end_course_time = datetime.strptime(f"{data['course_date']} {data['end_course_time']}",
                                                    "%Y-%m-%d %H:%M")
                all_group_tutoring = GroupCoach.objects.filter(
                    project_bundle__project=project, project_group_coach__theme=data['theme'],
                    project_group_coach__start_time=start_course_time,
                    project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
                    project_group_coach__end_time=end_course_time, project_group_coach__place=data['course_place'],
                    inter_view_record_template_id=data['template_id'], deleted=False)
                if is_5alc:
                    for project_member in project_member_list:
                        if GroupCoach.objects.filter(
                                project_bundle__project_member=project_member,
                                project_group_coach__theme=data['theme'],
                                project_group_coach__place=data['course_place'],
                                project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
                                interview__coachee_record_status=True).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})
                data['total_count'] = all_group_tutoring.count()

                project_interview = ProjectInterview.objects.filter(
                    Q(coach_record_status=True) | Q(coachee_record_status=True),
                    topic=data['theme'], place_category=3, place=data['course_place'], type=1,
                    public_attr__type=1, public_attr__project=project,
                    public_attr__start_time=start_course_time,
                    public_attr__end_time=end_course_time,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
                    deleted=False, interview_subject=5).distinct()

                data['completed_count'] = project_interview.count()

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_group_tutoring.count() == project_interview.filter(
                        public_attr__end_time__lt=datetime.now()).count() and all_group_tutoring.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.coach_tasks:
                all_coach_task = CoachTask.objects.filter(project_bundle__project=project, hours=data['hours'],
                                                          template_id=data['template_id'], deleted=False)
                template = TotalTemplate.objects.filter(id=data['template_id'], deleted=False).first()
                if template.write_role == InterviewRecordTemplateRoleEnum.coach:
                    completed_coach_task = all_coach_task.filter(coach_submit_time__isnull=False)
                elif template.write_role == InterviewRecordTemplateRoleEnum.student:
                    completed_coach_task = all_coach_task.filter(coachee_submit_time__isnull=False)
                elif template.write_role == InterviewRecordTemplateRoleEnum.coach_student:
                    completed_coach_task = all_coach_task.filter(coachee_submit_time__isnull=False,
                                                                 coach_submit_time__isnull=False)
                else:
                    completed_coach_task = all_coach_task.filter(stakeholder_submit_time__isnull=False)
                if is_5alc:
                    for project_member in project_member_list:
                        if CoachTask.objects.filter(
                                project_bundle__project_member=project_member, hours=data['hours'],
                                template_id=data['template_id'], deleted=False).first() in list(completed_coach_task):
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_coach_task.count() == completed_coach_task.count() and all_coach_task.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.interview:
                # 所有的
                # 所有的配置了一对一辅导的人
                all_project_member = list(OneToOneCoach.objects.filter(
                    deleted=False, project_bundle__project=project).values_list('project_bundle__project_member_id',
                                                                                flat=True))
                all_count = len(list(set(all_project_member)))
                completed_count = 0
                for project_member_id in list(set(all_project_member)):
                    project_member = ProjectMember.objects.filter(id=project_member_id).first()
                    interviews_count = ProjectInterview.objects.filter(
                        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                        public_attr__type=ATTR_TYPE_INTERVIEW,
                        type=ProjectInterviewTypeEnum.formal_interview, public_attr__project=project_member.project,
                        deleted=False,
                        public_attr__target_user=project_member.user,
                        order__isnull=True
                    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()
                    if interviews_count >= data['count']:
                        completed_count += 1

                if is_5alc:
                    for project_member in project_member_list:
                        interviews_count = ProjectInterview.objects.filter(
                            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                            public_attr__type=ATTR_TYPE_INTERVIEW,
                            type=ProjectInterviewTypeEnum.formal_interview, public_attr__project=project_member.project,
                            deleted=False,
                            public_attr__target_user=project_member.user,
                            order__isnull=True
                        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()
                        if interviews_count == data['count']:
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})
                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif completed_count == all_count and all_count != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.growth_goals:
                if data['type'] == GrowthGoalsModelTypeEnum.fixed:
                    all_growth_goals = GrowthGoalsModule.objects.filter(
                        deleted=False, project_bundle__project=project, type=data['type'], end_date=data['end_date'],
                        start_date=data['start_date'])
                else:
                    all_growth_goals = GrowthGoalsModule.objects.filter(
                        deleted=False, project_bundle__project=project, type=data['type'], hours=data['hours'])

                if is_5alc:
                    for project_member in project_member_list:
                        if all_growth_goals.filter(project_bundle__project_member=project_member,
                                                   growth_goals__isnull=False).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})
                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_growth_goals.count() == all_growth_goals.filter(growth_goals__isnull=False).count() and \
                        all_growth_goals.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.change_observation:
                all_change_observation = ChangeObservation.objects.filter(
                    project_member__project=project, deleted=False, write_condition=data['write_condition'],
                    remind_type=data['remind_type'])

                if is_5alc:
                    for project_member in project_member_list:
                        if all_change_observation.filter(project_member=project_member, is_complete=True).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_change_observation.count() == all_change_observation.filter(is_complete=True).count() and \
                        all_change_observation.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.article:
                all_article_module = ArticleModule.objects.filter(
                    project_bundle__project=project, article_id=data['article_id'], start_time=data['start_date'],
                    end_time=data['end_date'], deleted=False)
                if is_5alc:
                    for project_member in project_member_list:
                        if all_article_module.filter(project_bundle__project_member=project_member,
                                                     start_time__lte=datetime.now(), is_complete=True).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif datetime.strptime(data['start_date'], '%Y-%m-%d') <= datetime.now() <= \
                        datetime.strptime(data['end_date'], '%Y-%m-%d'):
                    data['status'] = 2
                else:
                    data['status'] = 3

            elif data['data_type'] == DataType.chemical_interview:
                all_chemical_interview = ChemicalInterviewModule.objects.filter(project_member__project=project,
                                                                                deleted=False)
                if is_5alc:
                    for project_member in project_member_list:
                        data['users'].append({"user_id": project_member.user_id,
                                              "true_name": project_member.user.cover_name,
                                              "head_image_url": project_member.user.head_image_url})

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_chemical_interview.count() == all_chemical_interview.filter(
                        coaches__chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                        deleted=False).distinct().count() and all_chemical_interview.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

            elif data['data_type'] == DataType.stakeholder_interview:
                all_stakeholder_interview = StakeholderInterviewModule.objects.filter(
                    project_member__project=project, deleted=False,
                    stakeholder_interview_number=data['stakeholder_interview_number'], duration=data['duration'],
                    start_date=data['start_date'], end_date=data['end_date'],
                    coach_template_id=data['coach_template_id'], report_template_id=data['report_template_id']
                )
                if is_5alc:
                    for project_member in project_member_list:
                        if all_stakeholder_interview.filter(project_member=project_member,
                                                            coach_task__report_url__isnull=False).exists():
                            data['users'].append({"user_id": project_member.user_id,
                                                  "true_name": project_member.user.cover_name,
                                                  "head_image_url": project_member.user.head_image_url})

                if data['order_time'] > datetime.now():
                    data['status'] = 1
                elif all_stakeholder_interview.count() == all_stakeholder_interview.filter(
                        coach_task__report_url__isnull=False).count() and all_stakeholder_interview.count() != 0:
                    data['status'] = 3
                else:
                    data['status'] = 2

    exists_users = []

    for stage_data in reversed(project_data['stage_data']):
        for data in reversed(stage_data['content']):
            new_users = []
            if data['users']:
                for user_info in data['users']:
                    if user_info['user_id'] not in exists_users:
                        exists_users.append(user_info['user_id'])
                        new_users.append(user_info)
            data['users'] = new_users
    return project_data


def get_project_progress_article_data(content, project_member_list):
    data = []
    for article in content:
        total_count = len(project_member_list)
        article_model = Article.objects.filter(id=article['article_id'], deleted=False).first()
        article_data = {
            "name": f"文章阅读-{article_model.title}", "data_type": DataType.article, "title": "文章完成进度",
            "date": f"{article['start_date']} ~ {article['end_date']}",
            "total_count": total_count, "complete_count": 0, "users": [],
            "start_date": datetime.strptime(article['start_date'], '%Y-%m-%d').date(),
            "end_date": datetime.strptime(article['end_date'], '%Y-%m-%d').date()}

        if datetime.now().date() < article_data['start_date']:
            article_data['status'] = 1  # 未开始
            article_data['complete_data'] = None
            article_data['not_complete_data'] = None
            for project_member in project_member_list:
                if not ArticleModule.objects.filter(
                        deleted=False, project_bundle__project_member=project_member, start_time=article['start_date'],
                        end_time=article['end_date'], article_id=article['article_id']).exists():
                    article_data['total_count'] -= 1
        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                member_article_module = ArticleModule.objects.filter(
                        deleted=False, project_bundle__project_member=project_member, start_time=article['start_date'],
                        end_time=article['end_date'], article_id=article['article_id']).first()
                if not member_article_module:
                    total_count -= 1
                    article_data['total_count'] -= 1
                    continue

                elif member_article_module.is_complete:
                    complete_data['member_count'] += 1
                    complete_data['member_list'].append(user_info)
                    article_data['users'].append(user_info)
                    article_data['complete_count'] += 1
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            article_data['complete_data'] = complete_data
            article_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                article_data['status'] = 3
            else:
                article_data['status'] = 2
        data.append(article_data)
    return data


def get_project_progress_evaluation_data(content, project_member_list):
    data = []
    for evaluation in content:
        total_count = len(project_member_list)
        evaluation_model = Evaluation.objects.get(id=evaluation['evaluation_id'])
        evaluation_data = {"data_type": DataType.evaluation, "users": [], "title": "测评完成进度",
                           "name": f"{evaluation_model.name}",
                           "date": f"{evaluation['start_date']} ~ {evaluation['end_date']}",
                           "total_count": total_count, "complete_count": 0, "status": 1,
                           "start_date": datetime.strptime(evaluation['start_date'], '%Y-%m-%d').date(),
                           "end_date": datetime.strptime(evaluation['end_date'], '%Y-%m-%d').date()
                           }
        if datetime.now().date() < evaluation_data['start_date']:
            evaluation_data['status'] = 1  # 未开始
            evaluation_data['complete_data'] = None
            evaluation_data['not_complete_data'] = None
            for project_member in project_member_list:
                if not EvaluationModule.objects.filter(
                        evaluation_id=evaluation['evaluation_id'], deleted=False, start_time=evaluation['start_date'],
                        end_time=evaluation['end_date'], project_bundle__project_member=project_member).exists():
                    evaluation_data['total_count'] -= 1
        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}

            for project_member in project_member_list:
                if not EvaluationModule.objects.filter(
                        evaluation_id=evaluation['evaluation_id'], deleted=False, start_time=evaluation['start_date'],
                        end_time=evaluation['end_date'], project_bundle__project_member=project_member).exists():
                    total_count -= 1
                    evaluation_data['total_count'] -= 1
                    continue

                public_attr = PublicAttr.objects.filter(
                    project=project_member.project, user=project_member.user, target_user__isnull=True,
                    type=ATTR_TYPE_EVALUATION_ANSWER) if evaluation_model.code == MANAGE_EVALUATION else \
                    PublicAttr.objects.filter(
                        project=project_member.project, user=project_member.user, target_user=project_member.user,
                        type=ATTR_TYPE_EVALUATION_ANSWER)
                user_info = get_project_progress_project_member_user_info(project_member)
                if public_attr.exists():
                    complete_data['member_count'] += 1
                    evaluation_data['complete_count'] += 1
                    complete_data['member_list'].append(user_info)
                    # 只有完成了测评，才记录用户
                    evaluation_data['users'].append(user_info)
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            evaluation_data['complete_data'] = complete_data
            evaluation_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                evaluation_data['status'] = 3
            else:
                evaluation_data['status'] = 2

        data.append(evaluation_data)
    return data


def get_interview_start_date(hours, interview_list):
    tmp_hours = 0
    for interview in interview_list:
        tmp_hours += interview['hour']
        if tmp_hours >= hours:
            return interview['complete_date'] + timedelta(days=1)
    return None


def get_project_progress_coach_task_data(content, project_member_list, interview_list):
    data = []
    for coach_task in content:
        total_count = len(project_member_list)
        hours = coach_task['hours']
        start_date = get_interview_start_date(hours, interview_list)
        if not start_date:
            start_date = coach_task['created_at'][:10]
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = start_date + timedelta(days=3)
        template = TotalTemplate.objects.filter(id=coach_task['template_id'], deleted=False).first()
        coach_task_data = {
            "data_type": DataType.coach_tasks, "title": "教练任务完成进度",
            "name": f"教练任务-{template.title}",
            "date": f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}",
            "users": [], "start_date": start_date, "end_date": end_date, "total_count": total_count,
            "complete_count": 0, "status": 1}
        if datetime.now().date() < start_date:
            coach_task_data['status'] = 1  # 未开始
            coach_task_data['complete_data'] = None
            coach_task_data['not_complete_data'] = None
            for project_member in project_member_list:
                member_coach_task = CoachTask.objects.filter(
                    project_bundle__project_member=project_member, hours=coach_task['hours'],
                    template_id=coach_task['template_id'], deleted=False).first()
                if not member_coach_task:
                    coach_task_data['total_count'] -= 1
        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                member_coach_task = CoachTask.objects.filter(
                    project_bundle__project_member=project_member, hours=coach_task['hours'],
                    template_id=coach_task['template_id'], deleted=False).first()
                if not member_coach_task:
                    total_count -= 1
                    coach_task_data['total_count'] -= 1
                    continue
                if (
                        member_coach_task.template.write_role == InterviewRecordTemplateRoleEnum.coach and member_coach_task.coach_submit_time) or \
                        (
                                member_coach_task.template.write_role == InterviewRecordTemplateRoleEnum.student and member_coach_task.coachee_submit_time) or \
                        (
                                member_coach_task.template.write_role == InterviewRecordTemplateRoleEnum.coach_student and member_coach_task.coach_submit_time and member_coach_task.coachee_submit_time) or \
                        (
                                member_coach_task.template.write_role == InterviewRecordTemplateRoleEnum.stakeholder and member_coach_task.stakeholder_submit_time):
                    complete_data['member_count'] += 1
                    coach_task_data['complete_count'] += 1
                    complete_data['member_list'].append(user_info)
                    coach_task_data['users'].append(user_info)
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            coach_task_data['complete_data'] = complete_data
            coach_task_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                coach_task_data['status'] = 3
            else:
                coach_task_data['status'] = 2

        data.append(coach_task_data)
    return data


def get_project_progress_growth_target_data(content, project_member_list, interview_list):
    data = []
    for growth_goal in content:
        total_count = len(project_member_list)
        if growth_goal['type'] == GrowthGoalsModelTypeEnum.limit:  # 辅导x小时后的
            hours = growth_goal['hours']
            start_date = get_interview_start_date(hours, interview_list)
            if not start_date:
                start_date = growth_goal['created_at'][:10]
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = start_date + timedelta(days=3)
            date = f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"
        else:  # 固定时间段
            start_date = datetime.strptime(growth_goal['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(growth_goal['end_date'], '%Y-%m-%d').date()
            date = f"{growth_goal['start_date']}~{growth_goal['end_date']}"

        growth_goal_data = {"data_type": DataType.growth_goals, "name": "成长目标", "users": [],
                            "date": date, "start_date": start_date, "end_date": end_date, "status": 1,
                            "complete_count": 0, "total_count": total_count, "title": "目标设置完成进度"}

        if datetime.now().date() < start_date:
            growth_goal_data['status'] = 1  # 未开始
            growth_goal_data['complete_data'] = None
            growth_goal_data['not_complete_data'] = None
            for project_member in project_member_list:
                member_growth_goal = GrowthGoalsModule.objects.filter(
                    project_bundle__project_member=project_member, type=growth_goal['type'],
                    start_date=growth_goal['start_date'], end_date=growth_goal['end_date'], deleted=False) \
                    if growth_goal['type'] == GrowthGoalsModelTypeEnum.fixed else \
                    GrowthGoalsModule.objects.filter(
                        project_bundle__project_member=project_member, type=growth_goal['type'],
                        hours=growth_goal['hours'], deleted=False)
                if not member_growth_goal.exists():
                    growth_goal_data['total_count'] -= 1

        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                member_growth_goal = GrowthGoalsModule.objects.filter(
                    project_bundle__project_member=project_member, type=growth_goal['type'],
                    start_date=growth_goal['start_date'], end_date=growth_goal['end_date'], deleted=False).first() \
                    if growth_goal['type'] == GrowthGoalsModelTypeEnum.fixed else \
                    GrowthGoalsModule.objects.filter(
                        project_bundle__project_member=project_member, type=growth_goal['type'],
                        hours=growth_goal['hours'], deleted=False).first()
                if not member_growth_goal:
                    total_count -= 1
                    growth_goal_data['total_count'] -= 1
                    continue

                elif member_growth_goal.growth_goals:
                    complete_data['member_count'] += 1
                    growth_goal_data['complete_count'] += 1
                    complete_data['member_list'].append(user_info)
                    growth_goal_data['users'].append(user_info)
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            growth_goal_data['complete_data'] = complete_data
            growth_goal_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                growth_goal_data['status'] = 3
            else:
                growth_goal_data['status'] = 2

        data.append(growth_goal_data)
    return data


def get_project_progress_workshop_data(content, project_member_list):
    data = []
    for workshop in content:
        total_count = len(project_member_list)
        workshop_data = {
            "name": f"工作坊-{workshop['theme']}",
            "data_type": DataType.group_coach, "users": [],
            "start_date": datetime.strptime(workshop['course_date'], '%Y-%m-%d').date(),
            "end_date": datetime.strptime(workshop['course_date'], '%Y-%m-%d').date(),
            "date": f"{workshop['course_date']} {workshop['start_course_time'][:5]}-{workshop['end_course_time'][:5]}",
            "total_count": total_count, "complete_count": 0, "status": 1, "title": "工作坊完成进度",
            "start_time": datetime.strptime(f"{workshop['course_date']} {workshop['start_course_time'][:5]}",
                                            "%Y-%m-%d %H:%M")
        }
        start_date = datetime.strptime(workshop['course_date'], '%Y-%m-%d').date()
        start_course_time = datetime.strptime(f"{workshop['course_date']} {workshop['start_course_time']}",
                                              "%Y-%m-%d %H:%M")
        end_course_time = datetime.strptime(f"{workshop['course_date']} {workshop['end_course_time']}",
                                            "%Y-%m-%d %H:%M")
        if datetime.now().date() < start_date:
            workshop_data['status'] = 1  # 未开始
            workshop_data['complete_data'] = None
            workshop_data['not_complete_data'] = None
            for project_member in project_member_list:
                interview = ProjectInterview.objects.filter(
                    topic=workshop['theme'], place_category=3, place=workshop['course_place'], type=1,
                    public_attr__type=1, public_attr__project=project_member.project,
                    public_attr__target_user=project_member.user,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                    public_attr__start_time=start_course_time,
                    public_attr__end_time=end_course_time,
                    deleted=False, interview_subject=5)
                if not interview.exists():
                    workshop_data['total_count'] -= 1
        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                interview = ProjectInterview.objects.filter(
                        topic=workshop['theme'], place_category=3, place=workshop['course_place'], type=1,
                        public_attr__type=1, public_attr__project=project_member.project,
                        public_attr__target_user=project_member.user,
                        coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                        public_attr__start_time=start_course_time,
                        public_attr__end_time=end_course_time,
                        deleted=False, interview_subject=5)
                if not interview.exists():
                    total_count -= 1
                    workshop_data['total_count'] -= 1
                    continue
                elif interview.filter(coachee_record_status=True).exists():
                    complete_data['member_count'] += 1
                    workshop_data['complete_count'] += 1
                    complete_data['member_list'].append(user_info)
                    workshop_data['users'].append(user_info)
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            workshop_data['complete_data'] = complete_data
            workshop_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                workshop_data['status'] = 3
            else:
                workshop_data['status'] = 2

        data.append(workshop_data)
    data = sorted(data, key=lambda x: x['start_time'])
    return data


def get_project_progress_group_tutoring_data(content, project_member_list):
    data = []
    for group_tutoring in content:
        total_count = len(project_member_list)
        group_tutoring_data = {
            "name": f"小组辅导-{group_tutoring['theme']}",
            "data_type": DataType.group_tutoring, "users": [],
            "start_date": datetime.strptime(group_tutoring['course_date'], '%Y-%m-%d').date(),
            "end_date": datetime.strptime(group_tutoring['course_date'], '%Y-%m-%d').date(),
            "date": f"{group_tutoring['course_date']} {group_tutoring['start_course_time'][:5]}-{group_tutoring['end_course_time'][:5]}",
            "total_count": total_count, "complete_count": 0, "status": 1, "title": "小组辅导完成进度",
            "start_time": datetime.strptime(f"{group_tutoring['course_date']} {group_tutoring['start_course_time'][:5]}",
                                            "%Y-%m-%d %H:%M")
        }
        start_date = datetime.strptime(group_tutoring['course_date'], '%Y-%m-%d').date()
        start_course_time = datetime.strptime(f"{group_tutoring['course_date']} {group_tutoring['start_course_time']}",
                                              "%Y-%m-%d %H:%M")
        end_course_time = datetime.strptime(f"{group_tutoring['course_date']} {group_tutoring['end_course_time']}",
                                            "%Y-%m-%d %H:%M")
        if datetime.now().date() < start_date:
            group_tutoring_data['status'] = 1  # 未开始
            group_tutoring_data['complete_data'] = None
            group_tutoring_data['not_complete_data'] = None
            for project_member in project_member_list:
                interview = ProjectInterview.objects.filter(
                    topic=group_tutoring['theme'], place_category=3, place=group_tutoring['course_place'], type=1,
                    public_attr__type=1, public_attr__project=project_member.project,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
                    public_attr__target_user=project_member.user,
                    public_attr__start_time=start_course_time,
                    public_attr__end_time=end_course_time,
                    deleted=False, interview_subject=5).distinct()
                if not interview.exists():
                    group_tutoring_data['total_count'] -= 1
        else:
            complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            not_complete_data = {"member_count": 0, "percent": 0, "color": "", "member_list": []}
            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                interview = ProjectInterview.objects.filter(
                    topic=group_tutoring['theme'], place_category=3, place=group_tutoring['course_place'], type=1,
                    public_attr__type=1, public_attr__project=project_member.project,
                    coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
                    public_attr__target_user=project_member.user,
                    public_attr__start_time=start_course_time,
                    public_attr__end_time=end_course_time,
                    deleted=False, interview_subject=5)
                if not interview.exists():
                    total_count -= 1
                    group_tutoring_data['total_count'] -= 1
                    continue
                elif interview.filter(coachee_record_status=True).exists():
                    complete_data['member_count'] += 1
                    group_tutoring_data['complete_count'] += 1
                    complete_data['member_list'].append(user_info)
                    group_tutoring_data['users'].append(user_info)
                else:
                    not_complete_data['member_count'] += 1
                    not_complete_data['member_list'].append(user_info)

            # 计算total_count和complete_percentage
            if total_count:
                total_count = complete_data['member_count'] + not_complete_data['member_count']
                complete_percentage = round((complete_data['member_count'] / total_count) * 100)

                # 更新complete_data和not_complete_data的percent键
                complete_data['percent'] = complete_percentage
                not_complete_data['percent'] = 100 - complete_percentage

            group_tutoring_data['complete_data'] = complete_data
            group_tutoring_data['not_complete_data'] = not_complete_data
            if complete_data['percent'] == 100:
                group_tutoring_data['status'] = 3
            else:
                group_tutoring_data['status'] = 2

        data.append(group_tutoring_data)
    data = sorted(data, key=lambda x: x['start_time'])
    return data


def get_chemical_interview_coach_question(interview, order):
    tmp_answer = InterviewRecordTemplateAnswer.objects.filter(interview=interview, deleted=False).first()
    if tmp_answer:
        template = tmp_answer.question.template
        try:
            question_id = eval(template.questions_order)[order-1]
        except Exception:
            return None
        answer = InterviewRecordTemplateAnswer.objects.filter(interview=interview, question_id=question_id,
                                                              deleted=False).first()
        return answer
    return None


def get_project_progress_chemical_interview_data(content, project_member_list):
    data = []
    project_member_list = [project_member for project_member in project_member_list
                           if project_member.chemical_interview.filter(deleted=False).exists()]
    total_count = len(project_member_list)
    for chemical_interview in content:
        chemical_interview_data = {"name": "化学面谈", "data_type": DataType.chemical_interview,
                                   "date": f"{chemical_interview['start_date']} ~ {chemical_interview['end_date']}",
                                   "start_date": datetime.strptime(chemical_interview['start_date'], '%Y-%m-%d').date(),
                                   "end_date": datetime.strptime(chemical_interview['end_date'], '%Y-%m-%d').date(),
                                   "users": [], "total_count": total_count, "complete_count": 0,
                                   "title": "化学面谈任务进度"}

        if datetime.now().date() < chemical_interview_data['start_date']:
            chemical_interview_data['status'] = 1  # 未开始
            member_list = [{"project_member_id": project_member.id, "true_name": project_member.user.cover_name}
                           for project_member in project_member_list]
            bar_data = [{"member_count": total_count, "text": "未预约", "color": "#FF7065", "member_list": member_list},
                        {"member_count": 0, "text": "已预约未面谈", "color": "#815FFF", "member_list": []},
                        {"member_count": 0, "text": "已面谈未匹配", "color": "#FFB765", "member_list": []},
                        {"member_count": 0, "text": "已匹配", "color": "#54DB92", "member_list": []}]
            chemical_interview_data['bar_data'] = bar_data
            chemical_interview_data['pie_data'] = []
        else:
            bar_data = [
                {"member_count": 0, "text": "未预约", "color": "#FF7065", "member_list": []},
                {"member_count": 0, "text": "已预约未面谈", "color": "#815FFF", "member_list": []},
                {"member_count": 0, "text": "已面谈未匹配", "color": "#FFB765", "member_list": []},
                {"member_count": 0, "text": "已匹配", "color": "#54DB92", "member_list": []}]
            pie_data = [{"name": "教练匹配度", "text": "管理者和教练的匹配程度",
                         "pie": [{"pie_text": "选择首次面谈的教练", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "未选择首次面谈的教练", "color": "#FF7065", "percent": 0}]},
                        {"name": "客户意愿度", "text": "管理者对教练项目的准备情况",
                         "pie": [{"pie_text": "意愿度低", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "意愿度中", "color": "#FFB765", "percent": 0},
                                 {"pie_text": "意愿度高", "color": "#FF7065", "percent": 0}]},
                        {"name": "目标清晰度", "text": "管理者对自己发展目标的清晰度",
                         "pie": [{"pie_text": "对项目目标和自己的发展目标都不清晰", "color": "#FF7065", "percent": 0},
                                 {"pie_text": "清楚项目目标，但不清楚自己的发展目标", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "清楚自己的发展目标，但和项目结合较弱", "color": "#FFB765", "percent": 0},
                                 {"pie_text": "对于项目目标和自己的发展目标都很清晰", "color": "#54DB92", "percent": 0}]},
                        ]
            show_pie = False
            select_count = 0
            readiness_low_count = 0
            readiness_middle_count = 0
            readiness_high_count = 0
            target_1 = 0   # 对项目目标和自己的发展目标都不清晰
            target_2 = 0   # 清楚项目目标，但不清楚自己的发展目标
            target_3 = 0   # 清楚自己的发展目标，但和项目结合较弱
            target_4 = 0   # 对于项目目标和自己的发展目标都很清晰

            for project_member in project_member_list:
                user_info = get_project_progress_project_member_user_info(project_member)
                member_chemical_interview_module = ChemicalInterviewModule.objects.filter(project_member=project_member,
                                                                                          deleted=False).first()

                # 已匹配
                if member_chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected).exists():
                    user_info["coach_list"] = get_project_progress_chemical_interview_coach_info(project_member, status=ChemicalInterviewStatusEnum.selected)
                    bar_data[3]['member_count'] += 1
                    bar_data[3]['member_list'].append(user_info)
                    chemical_interview_data['complete_count'] += 1
                    chemical_interview_data['users'].append(user_info)
                # 一次都没预约
                elif member_chemical_interview_module.coaches.filter(deleted=False).count() == \
                    member_chemical_interview_module.coaches.filter(deleted=False, interview__isnull=True).count():
                    bar_data[0]['member_count'] += 1
                    bar_data[0]['member_list'].append(user_info)
                # 已预约未面谈  辅导时间大于当前时间的辅导存在，而且辅导时间小于当前时间的辅导不存在
                elif member_chemical_interview_module.coaches.filter(
                        deleted=False, interview__public_attr__start_time__gt=datetime.now()).exists() and \
                    not member_chemical_interview_module.coaches.filter(
                            deleted=False, interview__public_attr__start_time__lt=datetime.now()).exists():
                    user_info["coach_list"] = get_project_progress_chemical_interview_coach_info(project_member)
                    bar_data[1]['member_count'] += 1
                    bar_data[1]['member_list'].append(user_info)
                # 已面谈未匹配：统计已完成至少一次面谈，但还未选择教练的人数
                elif member_chemical_interview_module.coaches.filter(
                        interview__public_attr__end_time__lt=datetime.now(), deleted=False).exclude(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected).exists():
                    user_info["coach_list"] = get_project_progress_chemical_interview_coach_info(project_member)
                    bar_data[2]['member_count'] += 1
                    bar_data[2]['member_list'].append(user_info)


                # 饼图数据计算
                # 教练匹配度  统计选择第一次面谈的教练的人数/总人数
                if member_chemical_interview_module.coaches.filter(
                        deleted=False, interview__isnull=False).order_by('interview__public_attr__start_time').first() \
                    == member_chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected).first() and \
                        member_chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected).first() != None:
                    select_count += 1
                # 客户意愿度  选取辅导记录模板中“化学面谈记录V2.0”中第2题的所有回答的选择分布展示数据
                if member_chemical_interview_module.coaches.filter(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).exists():
                    interview = member_chemical_interview_module.coaches.filter(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).first().interview
                    answer = get_chemical_interview_coach_question(interview, 2)
                    if answer and answer.question.template.name == '化学面谈记录 V2.0':
                        show_pie = True
                        if answer.option.title == '意愿度高':
                            readiness_high_count += 1
                        elif answer.option.title == '意愿度中等':
                            readiness_middle_count += 1
                        else:
                            readiness_low_count += 1

                        answer = get_chemical_interview_coach_question(interview, 3)
                        if answer.option.title == '对项目目标和自己的发展目标都不清晰':
                            target_1 += 1
                        elif answer.option.title == '清楚项目目标，但不清楚自己的发展目标':
                            target_2 += 1
                        elif answer.option.title == '清楚自己的发展目标，但和项目结合较弱':
                            target_3 += 1
                        else:
                            target_4 += 1

            if show_pie:
                if select_count == 0:
                    select_percentage = 0
                else:
                    select_percentage = round((select_count / total_count) * 100)
                pie_data[0]['pie'][0]['percent'] = select_percentage
                pie_data[0]['pie'][1]['percent'] = 100 - select_percentage

                total_readiness_count = readiness_low_count + readiness_middle_count + readiness_high_count
                if total_readiness_count == 0:
                    readiness_low_count_percentage, readiness_middle_count_percentage, \
                    readiness_high_count_percentage = 0, 0, 0
                else:
                    if readiness_low_count == 0:
                        readiness_low_count_percentage = 0
                    else:
                        readiness_low_count_percentage = round((readiness_low_count/total_readiness_count) * 100)

                    if readiness_middle_count == 0:
                        readiness_middle_count_percentage = 0
                    else:
                        readiness_middle_count_percentage = round((readiness_middle_count/total_readiness_count) * 100)

                    if readiness_high_count == 0:
                        readiness_high_count_percentage = 0
                    else:
                        readiness_high_count_percentage = 100 - readiness_low_count_percentage - \
                                                          readiness_middle_count_percentage
                pie_data[1]['pie'][0]['percent'] = readiness_low_count_percentage
                pie_data[1]['pie'][1]['percent'] = readiness_middle_count_percentage
                pie_data[1]['pie'][2]['percent'] = readiness_high_count_percentage


                total_target_count = target_1 + target_2 + target_3 + target_4
                if not total_target_count:
                    target_1_percentage, target_2_percentage, target_3_percentage, target_4_percentage = 0, 0, 0, 0
                else:
                    if target_1 == 0:
                        target_1_percentage = 0
                    else:
                        target_1_percentage = round((target_1 / total_target_count) * 100)

                    if target_2 == 0:
                        target_2_percentage = 0
                    else:
                        target_2_percentage = round((target_2 / total_target_count) * 100)

                    if target_3 == 0:
                        target_3_percentage = 0
                    else:
                        target_3_percentage = round((target_3 / total_target_count) * 100)

                    if target_4 == 0:
                        target_4_percentage = 0
                    else:
                        target_4_percentage = 100 - target_1_percentage - target_2_percentage - target_3_percentage
                pie_data[2]['pie'][0]['percent'] = target_1_percentage
                pie_data[2]['pie'][1]['percent'] = target_2_percentage
                pie_data[2]['pie'][2]['percent'] = target_3_percentage
                pie_data[2]['pie'][3]['percent'] = target_4_percentage
            else:
                pie_data = []

            chemical_interview_data['bar_data'] = bar_data
            chemical_interview_data['pie_data'] = pie_data
            if bar_data[3]['member_count'] == total_count:
                chemical_interview_data['status'] = 3
            else:
                chemical_interview_data['status'] = 2

        data.append(chemical_interview_data)
    return data


def get_project_progress_one_to_one_interview_data(content, project_member_list, interview_list):
    data = []
    project_member_list = [project_member for project_member in project_member_list
                           if OneToOneCoach.objects.filter(project_bundle__project_member=project_member,
                                                           deleted=False).exists()]
    total_count = len(project_member_list)
    for interview_service in content:
        interview_data = {
            "name": "一对一辅导", "data_type": DataType.interview, "date": None, "start_date": None,
            "end_date": None, "total_count": total_count, "complete_count": 0, "status": 1, "users": [],
            "title": "辅导进度"
        }
        start_date = datetime.strptime(interview_service['suggested_start_date'], '%Y-%m-%d').date()
        count = math.ceil(interview_service['hours'] / interview_service['suggested_duration'])
        end_date = start_date + timedelta(days=count * 7 * interview_service['suggested_interval'] -1)
        interview_data['date'] = f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"
        interview_data['start_date'] = start_date
        interview_data['end_date'] = end_date
        if start_date <= datetime.now().date():
            interview_data['status'] = 2
        
        bar_data = [{"member_count": 0, "text": "未预约", "color": "#FF7065", "member_list": []},
                    {"member_count": 0, "text": "进行中", "color": "#815FFF", "member_list": []},
                    {"member_count": 0, "text": "已完成", "color": "#54DB92", "member_list": []}]
        pie_data = []
        tmp_pie = []
        for project_member in project_member_list:
            # 有没有预约过
            user_info = get_project_progress_project_member_user_info(project_member)
            project_interview = ProjectInterview.objects.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                public_attr__target_user=project_member.user, public_attr__project=project_member.project,
                type=ProjectInterviewTypeEnum.formal_interview, deleted=False,
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')
            all_used_times = project_interview.aggregate(used_times=Sum('times'))
            all_used_times = all_used_times.get('used_times', 0) if all_used_times.get('used_times', 0) else 0
            all_used_times = round(all_used_times / 60, 1) if all_used_times > 0 else 0

            if all_used_times == 0:  # 未预约：第一次辅导还没开始预约的人数
                if project_interview.exists():
                    interview_time = project_interview.last().public_attr.start_time.strftime('%Y-%m-%d %H:%M')
                else:
                    interview_time = None
                user_info['interview_time'] = interview_time
                bar_data[0]['member_count'] += 1
                bar_data[0]['member_list'].append(user_info)
                is_update = False
                for pie in tmp_pie:
                    if pie['member_interview_count'] == 0:
                        pie['member_list'].append({"project_member_id": project_member.id,
                                                   "true_name": project_member.user.cover_name,
                                                   "interview_time": interview_time})
                        is_update = True
                if not is_update:
                    tmp_pie.append({"pie_text": f"辅导0次", "color": "",
                                    "member_interview_count": 0,
                                    "member_list": [{"project_member_id": project_member.id,
                                                     "true_name": project_member.user.cover_name,
                                                     "interview_time": interview_time}]})
            else:
                project_interview = ProjectInterview.objects.filter(
                    public_attr__target_user=project_member.user, public_attr__project=project_member.project,
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                    type=ProjectInterviewTypeEnum.formal_interview, deleted=False, coach_record_status=True,
                    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')
                complete_used_times = project_interview.aggregate(used_times=Sum('times'))
                complete_used_times = complete_used_times.get('used_times', 0) if complete_used_times.get('used_times', 0) else 0
                complete_used_times = round(complete_used_times / 60, 1) if complete_used_times > 0 else 0

                if project_interview.exists():
                    interview_time = project_interview.last().public_attr.start_time.strftime('%Y-%m-%d %H:%M')

                else:
                    # 有预约但是都没完成
                    interview_time = ProjectInterview.objects.filter(
                        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
                        public_attr__target_user=project_member.user, public_attr__project=project_member.project,
                        type=ProjectInterviewTypeEnum.formal_interview, deleted=False,
                        ).exclude(
                        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL
                    ).order_by('public_attr__start_time').last().public_attr.start_time.strftime('%Y-%m-%d %H:%M')

                user_info['interview_time'] = interview_time
                
                # 计算客户在辅导服务中是否完成
                hours = interview_service['hours'] # 当前服务需要的时长
                last_hours = 0 # 上一个服务需要的时长
                # 在循环外部预先获取所有需要的数据
                # project_member_services = ProjectMemberServiceContent.objects.filter(
                #     project_service_members__project_service__project_id=project_member.project.id,
                #     project_service_members__deleted=False
                # ).values('project_member_id', 'project_service_members__project_service__content')

                # # 创建一个字典来存储每个project_member的服务内容
                # member_services = {}
                # for service in project_member_services:
                #     member_id = service['project_member_id']
                #     content = service['project_service_members__project_service__content']
                #     if member_id not in member_services:
                #         member_services[member_id] = []
                #     member_services[member_id].extend(content)

                # # 在循环中使用预先获取的数据
                # for index, tmp_interview_service in enumerate(interview_list):
                #     if tmp_interview_service['id'] == interview_service['id']:
                #         break
                #     # 如果当前客户配置了这个辅导服务，就需要计算累积时长
                #     if project_member.id in member_services:
                #         member_content = member_services[project_member.id]
                #         if any(service.get('id') == tmp_interview_service['id'] for service in member_content):
                #             last_hours += tmp_interview_service['hours']
                            
                for index, tmp_interview_service in enumerate(interview_list):
                    if tmp_interview_service['id'] == interview_service['id']:
                        break
                    # 如果当前客户配置了这个辅导服务，就需要计算累积时长
                    if ProjectMember.objects.filter(id=project_member.id, 
                                                    project_member_service_content__project_service_members__project_service__content__contains=[{'id': tmp_interview_service['id']}],
                                                    project_member_service_content__project_service_members__deleted=False).exists():
                        last_hours += tmp_interview_service['hours']
                # 完成时长 > 需要的时长 = 已完成
                if complete_used_times >= (hours + last_hours):
                    bar_data[2]['member_count'] += 1
                    bar_data[2]['member_list'].append(user_info)
                    interview_data['users'].append(user_info)
                    interview_data['complete_count'] += 1
                # 当前阶段需要的最低辅导时长 < 预约时长 = 进行中
                elif all_used_times > last_hours:
                    bar_data[1]['member_count'] += 1
                    bar_data[1]['member_list'].append(user_info)
                # 都不满足 = 未开始
                else:
                    bar_data[0]['member_count'] += 1
                    bar_data[0]['member_list'].append(user_info)
                    
                exists_hours = 0
                member_interview_count = 0
                interview_time = None

                for interview in project_interview:
                    interview_hours = round(int(interview.times) / 60, 1)
                    exists_hours += interview_hours
                    if last_hours < exists_hours <= (last_hours + hours):
                        member_interview_count += 1
                        interview_time = interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')

                is_update = False
                for pie in tmp_pie:
                    if pie['member_interview_count'] == member_interview_count:
                        pie['member_list'].append({"project_member_id": project_member.id,
                                                   "true_name": project_member.user.cover_name,
                                                   "interview_time": interview_time})
                        is_update = True
                if not is_update:
                    tmp_pie.append({"pie_text": f"辅导{member_interview_count}次", "color": "",
                                    "member_interview_count": member_interview_count,
                                    "member_list": [{"project_member_id": project_member.id,
                                                     "true_name": project_member.user.cover_name,
                                                     "interview_time": interview_time}]})

        if tmp_pie:

            # 辅导总人数
            total_count = 0
            for pie in tmp_pie:
                total_count += len(pie.get('member_list'))

            exists_percent = 0
            color_list = ['#815FFF', '#FF7065', '#FFB765', '#8195FF', '#815FFF', '#FFEF5F', '#FF65D4', '#91E2D9',
                          '#FF8A8A', '#8F00FF', '#7ED887']
            for index, obj in enumerate(tmp_pie):
                if index != len(tmp_pie) - 1:
                    percentage = (len(obj.get('member_list')) / total_count) * 100
                    obj['percent'] = int(percentage)
                    exists_percent += int(percentage)
                else:
                    obj['percent'] = 100 - exists_percent
                if index <= len(color_list) - 1:
                    obj['color'] = color_list[index]
                else:
                    obj['color'] = color_list[-1]
                # obj.pop('member_interview_count')

            # 将tmp_pie重新排序，按照辅导次数
            tmp_pie = sorted(tmp_pie, key=lambda x: x['member_interview_count'])
            pie_data = [{"name": "辅导次数分布", "text": None, "pie": tmp_pie}]

        interview_data['bar_data'] = bar_data
        if count > 1:   
            # 大于1次辅导才显示饼图
            interview_data['pie_data'] = pie_data

        if bar_data[2]['member_count'] == total_count:
            interview_data['status'] = 3

        elif bar_data[1]['member_count']:
            interview_data['status'] = 2

        data.append(interview_data)
    return data


def get_project_progress_stakeholder_interview_data(content, project_member_list):
    data = []
    for stakeholder_interview in content:
        total_count = len(project_member_list)
        stakeholder_interview_data = {
            "name": "利益相关者访谈", "data_type": DataType.stakeholder_interview, "users": [],
            "date": f"{stakeholder_interview['start_date']}~{stakeholder_interview['end_date']}",
            "total_count": total_count, "complete_count": 0, "status": 1, "title": "访谈进度",
        }
        start_date = datetime.strptime(stakeholder_interview['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(stakeholder_interview['end_date'], '%Y-%m-%d').date()
        stakeholder_interview_data['start_date'] = start_date
        stakeholder_interview_data['end_date'] = end_date
        if start_date <= datetime.now().date():
            stakeholder_interview_data['status'] = 2

        bar_data = [{"member_count": 0, "text": "未开始", "color": "#FF7065", "member_list": []},
                    {"member_count": 0, "text": "进行中", "color": "#815FFF", "member_list": []},
                    {"member_count": 0, "text": "已完成", "color": "#54DB92", "member_list": []}]

        report_list = []

        for project_member in project_member_list:
            user_info = get_project_progress_project_member_user_info(project_member)
            stakeholder_interview_module = StakeholderInterviewModule.objects.filter(
                project_member=project_member, deleted=False,
                stakeholder_interview_number=stakeholder_interview['stakeholder_interview_number'],
                duration=stakeholder_interview['duration'], start_date=stakeholder_interview['start_date'],
                end_date=stakeholder_interview['end_date'], coach_template_id=stakeholder_interview['coach_template_id'],
                report_template_id=stakeholder_interview['report_template_id']).first()
            if not stakeholder_interview_module:
                total_count -= 1
                continue
            elif stakeholder_interview_module.stakeholder_interview.filter(deleted=False).count() == \
                 stakeholder_interview_module.stakeholder_interview.filter(interview__isnull=True, deleted=False).count():
                bar_data[0]['member_count'] += 1
                bar_data[0]['member_list'].append(user_info)
            else:
                summary_report = PersonalReport.objects.filter(
                    object_id=stakeholder_interview_module.coach_task_id, deleted=False, pdf_url__isnull=False,
                    type=PersonalReportTypeEnum.summary_report.value).first()
                if summary_report:
                    bar_data[2]['member_count'] += 1
                    bar_data[2]['member_list'].append(user_info)
                    stakeholder_interview_data['complete_count'] += 1
                    stakeholder_interview_data['users'].append(user_info)
                    report_list.append({"id": stakeholder_interview_module.coach_task.id,
                                        "project_member_id": project_member.id,
                                        "report_name": summary_report.name,
                                        "report_url": summary_report.pdf_url,
                                        "true_name": project_member.user.cover_name,
                                        "updated_at": stakeholder_interview_module.coach_task.updated_at})
                else:
                    bar_data[1]['member_count'] += 1
                    bar_data[1]['member_list'].append(user_info)
                    stakeholder_interview_data['users'].append(user_info)
        stakeholder_interview_data['bar_data'] = bar_data

        stakeholder_interview_data['total_count'] = total_count
        if bar_data[2]['member_count'] == total_count:
            stakeholder_interview_data['status'] = 3
        elif bar_data[1]['member_count']:
            stakeholder_interview_data['status'] = 2

        report_list = sorted(report_list, key=lambda x: x['updated_at'])
        for i, item in enumerate(report_list):
            item['order'] = i + 1
            item.pop('updated_at')

        stakeholder_interview_data['report_list'] = report_list
        data.append(stakeholder_interview_data)

    return data


def get_project_progress_change_observation_data(content, project_member_list, interview_list):
    data = []
    for change_observation in content:
        total_count = len(project_member_list)
        change_observation_data = {"data_type": DataType.change_observation, "name": "改变观察调研", "users": [],
                                   "date": None, "start_date": None, "end_date": None,
                                   "complete_count": 0, "status": 1, "title": "调研进度"}
        hours = change_observation['write_condition']
        start_date = get_interview_start_date(hours, interview_list)
        if not start_date:
            start_date = change_observation['created_at'][:10]
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if start_date <= datetime.now().date():
            change_observation_data['status'] = 2
        end_date = start_date + timedelta(days=3)
        change_observation_data['date'] = f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"
        change_observation_data['start_date'] = start_date
        change_observation_data['end_date'] = end_date

        bar_data = [{"member_count": 0, "text": "未开始", "color": "#FF7065", "member_list": []},
                    {"member_count": 0, "text": "进行中", "color": "#815FFF", "member_list": []},
                    {"member_count": 0, "text": "已完成", "color": "#54DB92", "member_list": []}]

        report_list = []

        for project_member in project_member_list:
            user_info = get_project_progress_project_member_user_info(project_member)
            member_change_observation = ChangeObservation.objects.filter(
                project_member_id=project_member, deleted=False, write_condition=change_observation['write_condition'],
                remind_type=change_observation['remind_type']).first()
            if not member_change_observation:
                total_count -= 1
                continue
            if not member_change_observation.change_observation_answers.exists():
                bar_data[0]['member_count'] += 1
                bar_data[0]['member_list'].append(user_info)

            else:
                if member_change_observation.is_complete:
                    bar_data[2]['member_count'] += 1
                    bar_data[2]['member_list'].append(user_info)
                    change_observation_data['complete_count'] += 1
                    change_observation_data['users'].append(user_info)

                    personal_report = PersonalReport.objects.filter(
                        type=PersonalReportTypeEnum.change_observation_report.value,
                        object_id=member_change_observation.pk, user=project_member.user,
                        project=member_change_observation.project_member.project, deleted=False).first()

                    report_list.append({"id": personal_report.id,
                                        "project_member_id": project_member.id,
                                        "report_name": f"{project_member.user.cover_name}的改变观察总结",
                                        "true_name": project_member.user.cover_name,
                                        "updated_at": personal_report.created_at})

                else:
                    bar_data[1]['member_count'] += 1
                    bar_data[1]['member_list'].append(user_info)
                    change_observation_data['users'].append(user_info)

        change_observation_data['bar_data'] = bar_data
        change_observation_data['total_count'] = total_count
        if bar_data[2]['member_count'] == total_count:
            change_observation_data['status'] = 3
        elif bar_data[1]['member_count']:
            change_observation_data['status'] = 2

        report_list = sorted(report_list, key=lambda x: x['updated_at'])
        for i, item in enumerate(report_list):
            item['order'] = i + 1
            item.pop('updated_at')

        change_observation_data['report_list'] = report_list
        data.append(change_observation_data)
    return data


def get_project_progress_project_member_user_info(project_member):
    """
    获取项目路径图用户信息
    project_member: 项目成员

    """
    user_info = {"project_member_id": project_member.id, "true_name": project_member.user.cover_name,
                 "name": project_member.user.name, 'company_member_id': project_member.company_member_id}
    
    return user_info


def get_project_progress_chemical_interview_coach_info(project_member, status=None):
    # 获取客户预约的教练信息
    coach_list = []
    chemical_interviews = ChemicalInterview2Coach.objects.filter(
        chemical_interview_module__project_member=project_member,
        deleted=False,
        interview__isnull=False  # 已预约的教练
    )

    if status:
        chemical_interviews = chemical_interviews.filter(chemical_interview_status=status)

    if chemical_interviews.exists():
        for interview in chemical_interviews:
            coach = interview.coach
            # 从项目定制化获取教练简历
            project_coach = ProjectCoach.objects.filter(
                project=project_member.project,
                coach=coach,
                deleted=False
            ).first()
            # 查询教练简历
            if project_coach and project_coach.show_resume:
                resume = project_coach.show_resume
            else:
                resume = coach.resumes.filter(deleted=False).first() if coach.resumes.filter(deleted=False).exists() else None
            
            coach_info = {
                "name": coach.user.true_name,
                "resume_id": resume.id if resume else None
            }
            coach_list.append(coach_info)
            
    return coach_list

def get_project_progress_data(project):
    """
    获得项目进展数据
    project: 项目
    """
    project_stages = ProjectServiceStage.objects.filter(project=project, deleted=False)
    if not project_stages:  # 服务阶段为空，不分阶段
        data = {'is_stage': False, 'stage_data': []}
        project_content = ProjectServiceContent.objects.filter(project=project, deleted=False).order_by('order')
        if not project_content.exists():
            return None
        stage_names = [None]
        project_stage = [list(project_content)]
        stage_ids = []
    else:
        data = {'is_stage': True, 'stage_data': []}
        stage_ids = list(project_stages.order_by('order').values_list('id', flat=True))
        stage_names = list(project_stages.order_by('order').values_list('stage_name', flat=True))

        project_stage = []
        for item in stage_ids:
            project_content = ProjectServiceContent.objects.filter(
                project=project, service_stage_id=item, deleted=False).order_by('order')
            project_stage.append(list(project_content))

    # 查询使用了项目配置的用户
    project_member_list = []
    project_members = ProjectMember.objects.filter(project=project, deleted=False)
    project_is_stage = ProjectServiceStage.objects.filter(project=project, deleted=False).exists()
    for project_member in project_members:
        if project_member.is_stage != project_is_stage:
            continue  # 用户与项目配置服务内容冲突，跳过
        if ProjectServiceStage.objects.filter(project_member=project_member, deleted=False).exists() and \
                project_member.is_stage:  # 用户分阶段但是使用的自定义的阶段，跳过
            continue
        if not project_member.is_stage and not ProjectMemberServiceContent.objects.filter(
                project_service_members__project_service__project_id=project.id,
                project_service_members__deleted=False).exists():  # 用户和项目不分阶段，跳过
            continue
        project_member_list.append(project_member)

    # 在外面找出所有的一对一辅导添加进列表，给教练任务及改变观察这些辅导x小时后的服务获取时间
    interview_contents = ProjectServiceContent.objects.filter(deleted=False, project=project, content__isnull=False,
                                                              content_type=DataType.interview)
    interview_list = []
    for interview_content in interview_contents:
        content = interview_content.content
        interview_list.extend(content)
    # 开始日期从小到大排序
    interview_module_list = sorted(interview_list, key=lambda x: datetime.strptime(x['suggested_start_date'], '%Y-%m-%d'))

    interview_list = split_project_service_interview(project)

    for index, content_list in enumerate(project_stage):
        stage_id = None
        stage_name = None if not data['is_stage'] else stage_names[index]
        if data['is_stage'] and stage_ids:
            stage_id = stage_ids[index]
        content_data = {"name": stage_name, "status": 1, "stage_member_count": 0, "content": [], "stage_date": None}
        for stage_content in content_list:  # stage_content 的类型是 ProjectServiceContent
            if stage_content.content:
                # 获取与当前 stage_content 相关的 ProjectMember，并确保它们在 project_member_list 中
                stage_project_members = ProjectMember.objects.filter(
                    id__in=[pm.id for pm in project_member_list],
                    project_member_service_content__project_service_members__project_service=stage_content,
                    project_member_service_content__project_service_members__deleted=False
                ).distinct()

                if stage_content.content_type == DataType.article:
                    article_data = get_project_progress_article_data(stage_content.content, stage_project_members)
                    content_data['content'].extend(article_data)
                elif stage_content.content_type == DataType.evaluation:
                    evaluation_data = get_project_progress_evaluation_data(stage_content.content, stage_project_members)
                    content_data['content'].extend(evaluation_data)
                elif stage_content.content_type == DataType.coach_tasks:
                    coach_task_data = get_project_progress_coach_task_data(stage_content.content, stage_project_members,
                                                                           interview_list)
                    content_data['content'].extend(coach_task_data)
                elif stage_content.content_type == DataType.growth_goals:
                    growth_target_data = get_project_progress_growth_target_data(stage_content.content,
                                                                                 stage_project_members, interview_list)
                    content_data['content'].extend(growth_target_data)
                elif stage_content.content_type == DataType.group_coach:
                    workshop_data = get_project_progress_workshop_data(stage_content.content, stage_project_members)
                    content_data['content'].extend(workshop_data)

                elif stage_content.content_type == DataType.group_tutoring:
                    group_tutoring_data = get_project_progress_group_tutoring_data(stage_content.content,
                                                                                   stage_project_members)
                    content_data['content'].extend(group_tutoring_data)

                elif stage_content.content_type == DataType.chemical_interview:
                    chemical_interview_data = get_project_progress_chemical_interview_data(stage_content.content,
                                                                                           stage_project_members)
                    content_data['content'].extend(chemical_interview_data)

                elif stage_content.content_type == DataType.interview:
                    one_to_one_interview_data = get_project_progress_one_to_one_interview_data(stage_content.content,
                                                                                               stage_project_members,
                                                                                               interview_module_list)
                    content_data['content'].extend(one_to_one_interview_data)

                elif stage_content.content_type == DataType.stakeholder_interview:
                    stakeholder_interview_data = get_project_progress_stakeholder_interview_data(
                        stage_content.content, stage_project_members)
                    content_data['content'].extend(stakeholder_interview_data)

                elif stage_content.content_type == DataType.change_observation:
                    change_observation_data = get_project_progress_change_observation_data(stage_content.content,
                                                                                           stage_project_members, interview_list)
                    content_data['content'].extend(change_observation_data)
        tmp_content_data = sorted(content_data['content'], key=lambda x: x['start_date'])
        start_date = tmp_content_data[0]['start_date'] if tmp_content_data else None
        tmp_content_data = sorted(content_data['content'], key=lambda x: x['end_date'])
        end_date = tmp_content_data[-1]['end_date'] if tmp_content_data else None

        # 获取所有状态值的列表
        status_list = []
        for content in content_data['content']:
            status = content.get('status')
            status_list.append(status)

        # 全部都是已完成
        if all(x == 3 for x in status_list):
            content_data['status'] = 3  # 已结束
        # 全部都是未开始
        elif  all(x == 1 for x in status_list):
            content_data['status'] = 1  # 未开始
        # 其他情况是进行中
        else:
            content_data['status'] = 2  # 进行中

        content_data['stage_date'] = f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}" if \
            start_date else None
        data['stage_data'].append(content_data)

    # 统计各阶段的人数
    # 对于测评，只有完成了测评，才记录人数
    # TODO：计算规则应该是：个人的所有服务都完成，才算完成阶段。
    for stage_data in data['stage_data']:
        all_member_ids = set()
        for t_data in stage_data['content']:
            stage_project_member_ids = set()
            if t_data['users']:
                stage_project_member_ids.update(user_info['project_member_id'] for user_info in t_data['users'])
                if not all_member_ids:
                    all_member_ids = stage_project_member_ids
                else:
                    all_member_ids.intersection_update(stage_project_member_ids)
        
        stage_data['stage_member_count'] = len(all_member_ids)

    return data


def is_time_overlap(start1, end1, start2, end2):
    if start1 <= end2 and start2 <= end1:
        return True
    else:
        return False


def get_project_progress_report_data(project, start_date, end_date):
    data = {"name": f"{project.name}", "date": f"{start_date}-{end_date}", "chemical_interview": None,
            "growth_goals": None, "interview": {"name": "一对一辅导", "interview_data": []}}

    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    end_date_time = datetime.combine(end_date, datetime.min.time())

    project_member_list = []
    project_members = ProjectMember.objects.filter(project=project, deleted=False)
    project_is_stage = ProjectServiceStage.objects.filter(project=project, deleted=False).exists()
    for project_member in project_members:
        if project_member.is_stage != project_is_stage:
            continue  # 用户与项目配置服务内容冲突，跳过
        if ProjectServiceStage.objects.filter(project_member=project_member, deleted=False).exists() and \
                project_member.is_stage:  # 用户分阶段但是使用的自定义的阶段，跳过
            continue
        if not project_member.is_stage and not ProjectMemberServiceContent.objects.filter(
                project_service_members__project_service__project_id=project.id,
                project_service_members__deleted=False).exists():  # 用户和项目不分阶段，跳过
            continue
        project_member_list.append(project_member)

    stage_content = ProjectServiceContent.objects.filter(
        deleted=False, project=project, content_type=DataType.chemical_interview, content__isnull=False).first()
    if stage_content:
        coach_ids = [project_member.coach_id for project_member in project_member_list]
        if datetime.strptime(stage_content.content[0]['start_date'], '%Y-%m-%d').date() < datetime.now().date() \
            and None in coach_ids:
            bar_data = [{"member_count": 0, "text": "未预约教练", "color": "#FF7065", "member_list": [],
                         "img_url": "https://static.qzcoach.com/admin/progressIcons/interview_before.png"},
                        {"member_count": 0, "text": "已预约教练", "color": "#815FFF", "member_list": [],
                         "img_url": "https://static.qzcoach.com/admin/progressIcons/interviewed_coach_icon.png"},
                        {"member_count": 0, "text": "已面谈未选择教练", "color": "#FFB765", "member_list": [],
                         "img_url": "https://static.qzcoach.com/admin/progressIcons/unselect_coach_icon.png"},
                        {"member_count": 0, "text": "已匹配教练", "color": "#54DB92", "member_list": [],
                         "img_url": "https://static.qzcoach.com/admin/progressIcons/coach_match_icon.png"}]
            pie_data = [{"name": "教练匹配度", "text": "管理者和教练的匹配程度",
                         "pie": [{"pie_text": "选择首次面谈的教练", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "未选择首次面谈的教练", "color": "#FF7065", "percent": 0}]},
                        {"name": "客户意愿度", "text": "管理者对教练项目的准备情况",
                         "pie": [{"pie_text": "意愿度低", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "意愿度中", "color": "#FFB765", "percent": 0},
                                 {"pie_text": "意愿度高", "color": "#FF7065", "percent": 0}]},
                        {"name": "目标清晰度", "text": "管理者对自己发展目标的清晰度",
                         "pie": [{"pie_text": "对项目目标和自己的发展目标都不清晰", "color": "#FF7065", "percent": 0},
                                 {"pie_text": "清楚项目目标，但不清楚自己的发展目标", "color": "#815FFF", "percent": 0},
                                 {"pie_text": "清楚自己的发展目标，但和项目结合较弱", "color": "#FFB765", "percent": 0},
                                 {"pie_text": "对于项目目标和自己的发展目标都很清晰", "color": "#54DB92", "percent": 0}]}]
            select_count = 0
            readiness_low_count = 0
            readiness_middle_count = 0
            readiness_high_count = 0
            target_1 = 0  # 对项目目标和自己的发展目标都不清晰
            target_2 = 0  # 清楚项目目标，但不清楚自己的发展目标
            target_3 = 0  # 清楚自己的发展目标，但和项目结合较弱
            target_4 = 0  # 对于项目目标和自己的发展目标都很清晰
            show_pie = False
            for project_member in project_member_list:
                user_info = {"project_member_id": project_member.id, "true_name": project_member.user.cover_name}
                project_coach = ProjectCoach.objects.filter(member=project_member.user, project=project_member.project,
                                                            project_group_coach__isnull=True, deleted=False).first()
                member_chemical_interview_module = ChemicalInterviewModule.objects.filter(
                    deleted=False, project_member=project_member).first()
                if not member_chemical_interview_module:
                    continue

                if project_coach:
                    if project_coach.created_at.date() <= end_date:
                        # 已匹配
                        bar_data[3]['member_count'] += 1
                        bar_data[3]['member_list'].append(user_info)
                else:
                    # 未匹配
                    # 已预约未面谈
                    if member_chemical_interview_module.coaches.filter(deleted=False).count() == \
                        member_chemical_interview_module.coaches.filter(deleted=False,
                                                                        interview__isnull=end_date_time).count():
                        bar_data[0]['member_count'] += 1
                        bar_data[0]['member_list'].append(user_info)

                    elif member_chemical_interview_module.coaches.filter(
                        deleted=False, interview__public_attr__start_time__gt=end_date_time).exists() and \
                        not member_chemical_interview_module.coaches.filter(
                        deleted=False, interview__public_attr__start_time__lt=end_date_time).exists():
                        bar_data[1]['member_count'] += 1
                        bar_data[1]['member_list'].append(user_info)

                    elif member_chemical_interview_module.coaches.filter(
                        interview__public_attr__end_time__lt=end_date_time, deleted=False).exclude(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected).exists():
                        bar_data[2]['member_count'] += 1
                        bar_data[2]['member_list'].append(user_info)

                # 饼图数据计算
                # 教练匹配度  统计选择第一次面谈的教练的人数/总人数
                chemical_interview = member_chemical_interview_module.coaches.filter(
                        deleted=False, interview__isnull=False).order_by('interview__public_attr__start_time').first()
                if chemical_interview and chemical_interview == member_chemical_interview_module.coaches.filter(
                            deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected).first() and \
                        chemical_interview.interview.public_attr.end_time < end_date_time:
                    select_count += 1

                # 客户意愿度  选取辅导记录模板中“化学面谈记录V2.0”中第2题的所有回答的选择分布展示数据
                if member_chemical_interview_module.coaches.filter(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).exists():
                    interview = member_chemical_interview_module.coaches.filter(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                        deleted=False).first().interview
                    if interview.public_attr.end_time < end_date_time:
                        answer = get_chemical_interview_coach_question(interview, 2)
                        if answer and answer.question.template.name == '化学面谈记录 V2.0':
                            show_pie = True
                            if answer.option.title == '意愿度高':
                                readiness_high_count += 1
                            elif answer.option.title == '意愿度中等':
                                readiness_middle_count += 1
                            else:
                                readiness_low_count += 1

                            answer = get_chemical_interview_coach_question(interview, 3)
                            if answer.option.title == '对项目目标和自己的发展目标都不清晰':
                                target_1 += 1
                            elif answer.option.title == '清楚项目目标，但不清楚自己的发展目标':
                                target_2 += 1
                            elif answer.option.title == '清楚自己的发展目标，但和项目结合较弱':
                                target_3 += 1
                            else:
                                target_4 += 1
            if show_pie:
                if select_count == 0:
                    select_percentage = 0
                else:
                    select_percentage = round((select_count / len(project_member_list)) * 100)
                pie_data[0]['pie'][0]['percent'] = select_percentage
                pie_data[0]['pie'][1]['percent'] = 100 - select_percentage

                total_readiness_count = readiness_low_count + readiness_middle_count + readiness_high_count
                if total_readiness_count == 0:
                    readiness_low_count_percentage, readiness_middle_count_percentage, \
                    readiness_high_count_percentage = 0, 0, 0
                else:
                    if readiness_low_count == 0:
                        readiness_low_count_percentage = 0
                    else:
                        readiness_low_count_percentage = round((readiness_low_count/total_readiness_count) * 100)

                    if readiness_middle_count == 0:
                        readiness_middle_count_percentage = 0
                    else:
                        readiness_middle_count_percentage = round((readiness_middle_count/total_readiness_count) * 100)

                    if readiness_high_count == 0:
                        readiness_high_count_percentage = 0
                    else:
                        readiness_high_count_percentage = 100 - readiness_low_count_percentage - \
                                                          readiness_middle_count_percentage
                pie_data[1]['pie'][0]['percent'] = readiness_low_count_percentage
                pie_data[1]['pie'][1]['percent'] = readiness_middle_count_percentage
                pie_data[1]['pie'][2]['percent'] = readiness_high_count_percentage


                total_target_count = target_1 + target_2 + target_3 + target_4
                if not total_target_count:
                    target_1_percentage, target_2_percentage, target_3_percentage, target_4_percentage = 0, 0, 0, 0
                else:
                    if target_1 == 0:
                        target_1_percentage = 0
                    else:
                        target_1_percentage = round((target_1 / total_target_count) * 100)

                    if target_2 == 0:
                        target_2_percentage = 0
                    else:
                        target_2_percentage = round((target_2 / total_target_count) * 100)

                    if target_3 == 0:
                        target_3_percentage = 0
                    else:
                        target_3_percentage = round((target_3 / total_target_count) * 100)

                    if target_4 == 0:
                        target_4_percentage = 0
                    else:
                        target_4_percentage = 100 - target_1_percentage - target_2_percentage - target_3_percentage
                pie_data[2]['pie'][0]['percent'] = target_1_percentage
                pie_data[2]['pie'][1]['percent'] = target_2_percentage
                pie_data[2]['pie'][2]['percent'] = target_3_percentage
                pie_data[2]['pie'][3]['percent'] = target_4_percentage
            else:
                pie_data = []
            bar_data = [bar_data[3], bar_data[1], bar_data[2], bar_data[0]]
            chemical_interview_data = {"name": "教练匹配", "chemical_interview_data": bar_data, "pie_data": pie_data}
            data['chemical_interview'] = chemical_interview_data

    growth_goal_data = [
        {"member_count": 0, "text": "已设置目标", "member_list": [],
         "img_url": "https://static.qzcoach.com/admin/progressIcons/targeted.png"},
        {"member_count": 0, "text": "未设置目标", "member_list": [],
         "img_url": "https://static.qzcoach.com/admin/progressIcons/untarget.png"}
    ]
    for project_member in project_member_list:
        user_info = {"project_member_id": project_member.id, "true_name": project_member.user.cover_name}
        if GrowthGoalsModule.objects.filter(project_bundle__project_member=project_member, deleted=False).exists():
            if GrowthGoalsModule.objects.filter(
                project_bundle__project_member=project_member, growth_goals__created_at__range=[start_date,
                                                                                                end_date]).exists():
                growth_goal_data[0]['member_count'] += 1
                growth_goal_data[0]['member_list'].append(user_info)
            else:
                growth_goal_data[1]['member_count'] += 1
                growth_goal_data[1]['member_list'].append(user_info)

    if growth_goal_data[0]['member_count'] != len(project_member_list) or \
        growth_goal_data[1]['member_count'] != len(project_member_list):
        growth_goals = {"name": "目标设置", "growth_goal_data": growth_goal_data}
        data['growth_goals'] = growth_goals
    if growth_goal_data[0]['member_count'] == 0:
        data['growth_goals'] = None

    interview_data = {}

    for project_member in project_member_list:
        # 统计辅导次数
        interview_count = ProjectInterview.objects.filter(
            Q(created_at__range=[start_date, end_date]) |
            Q(public_attr__end_time__range=[start_date, end_date]),
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
            public_attr__project=project, type=ProjectInterviewTypeEnum.formal_interview,
            deleted=False,
            public_attr__target_user=project_member.user
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()

        if interview_data.get(interview_count):
            interview_data[interview_count].get('member_list').append(
                {"project_member_id": project_member.id, "true_name": project_member.user.cover_name})
            interview_data[interview_count]['member_count'] += 1
        else:
            interview_data[interview_count] = {
                "member_count": 1, "text": "", "member_list": [
                    {"project_member_id": project_member.id, "true_name": project_member.user.cover_name}],
                "img_url": "https://static.qzcoach.com/admin/progressIcons/interviewed_coach_icon.png"}

    sorted_data = dict(sorted(interview_data.items()))
    interview_data_list = []

    # 获取切分好的辅导
    split_interview_list = split_project_service_interview(project)

    # 获取最近的一次辅导次数
    min_diff = float('inf')
    closest_count = None
    for item in split_interview_list:
        item_date = item['order_time'].date()
        diff = abs((item_date - datetime.now().date()).days)
        if diff < min_diff:
            min_diff = diff
            closest_count = item['count']

    for k, v in sorted_data.items():
        v['text'] = f'辅导{k}次'
        v['interview_count'] = k
        v['is_planned'] = True if closest_count == k else False
        interview_data_list.append(v)
    data['interview']['interview_data'] = interview_data_list
    return data
