import datetime
import numpy as np
from rest_framework import serializers

from co_wisdom_server.base import NOT_SELECT_IMAGE_URL
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.enum.service_content_enum import NewCoachTaskTypeEnum, CoachOfferStatusEnum, CustomerPortraitTypeEnum
from wisdom_v2.common import schedule_public, coach_public
from wisdom_v2.models import Coach, ProjectInterview, TraineeCoach, CoachTask, ProjectMember, CoachOffer, CustomerPortrait
from django.db.models import Sum

from utils import int_arg
from wisdom_v2.models_file import ChemicalInterview2Coach
from wisdom_v2.views.constant import SCHEDULE_INTERVAL_MINUTES, INTERVIEW_TYPE_COACHING
from django.core.cache import cache

def get_time_point(start_date_time_str, during, user_id, time_status=False, apply_type=None):
    """
    传入某一天的datetime时间格式的字符串，获取当天教练最近的可用时间。
    :param start_date_time_str:  开始时间 type: str
    :param during:  开始时间与结束时间的间隔（分钟） type: int
    :param user_id: 需要查询日程的用户id
    :param time_status: 日程可预约状态 默认 不可预约
    :param apply_type: 日程适用类型 默认不限
    :return: 最近可用时间 or Null
    """
    
    start_date_time = datetime.datetime.strptime(start_date_time_str, '%Y-%m-%d %H:%M:%S')

    if start_date_time.minute > 45:
        start_date_time = start_date_time.replace(minute=0) + datetime.timedelta(hours=1)
    elif start_date_time.minute > 30:
        start_date_time = start_date_time.replace(minute=45)
    elif start_date_time.minute > 15:
        start_date_time = start_date_time.replace(minute=30)
    elif start_date_time.minute > 0:
        start_date_time = start_date_time.replace(minute=15)

    end_date_time = start_date_time + datetime.timedelta(minutes=during)
    if start_date_time.date() != end_date_time.date():
        return

    cache_key = f'get_time_point_{user_id}_{start_date_time_str}_{during}_{time_status}_{apply_type}'
    cached_result = cache.get(cache_key)
    if cached_result:
        if type(cached_result) == dict:
            return cached_result['time_point']
        else:
            cache.delete(cache_key)
    
    time_slots = schedule_public.get_schedule_day_list(user_id, start_date_time, time_status=time_status, apply_type=apply_type)

    new_data = end_date_time.date()

    while end_date_time.date() == new_data:
        state = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
        if state:
            cache.set(cache_key, {'time_point': start_date_time.strftime('%Y-%m-%d %H:%M:%S')}, timeout=60*15)
            return start_date_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            start_date_time = start_date_time + datetime.timedelta(minutes=SCHEDULE_INTERVAL_MINUTES)
            end_date_time = end_date_time + datetime.timedelta(minutes=SCHEDULE_INTERVAL_MINUTES)
    cache.set(cache_key, {'time_point': None}, timeout=60*30)
    return


def get_time_slot(coach_user, start_date, end_date, step, time_status=False, interview_id=None, apply_type=None):
    """
    获取指定教练在指定日期范围内，按照给定时间步长和状态可预约的时间段。

    参数:
    - coach_user: 教练对象，用于确定教练的ID。
    - start_date: 开始日期，datetime类型，查询的时间段起始。
    - end_date: 结束日期，datetime类型，查询的时间段结束。
    - step: 时间步长，以分钟为单位，表示每个时间段的长度。
    - time_status: 时间状态，默认可用时间段。
    - interview_id: 日程查询中需要排除的interview_id，int类型，可选参数，默认为None。

    返回值:
    - time_slot: 列表，包含字典，每个字典表示一个时间段及其是否可用的信息。
    """
    time_slot = []
    end_date = end_date + datetime.timedelta(minutes=SCHEDULE_INTERVAL_MINUTES)

    # 按照间隔进行时间循环
    date_list = np.arange(start_date, end_date, dtype='datetime64[m]', step=SCHEDULE_INTERVAL_MINUTES)
    time_slots = schedule_public.get_schedule_day_list(coach_user.id, start_date, time_status=time_status, interview_id=interview_id, apply_type=apply_type)

    for index, date in enumerate(date_list):
        start_time = (date_list[index]).astype(datetime.datetime)

        # 结束时间取决入传入时间间隔
        end_time = start_time + datetime.timedelta(minutes=step)
        if start_time.date() != end_time.date():
            break

        time_slot.append(
            {'time_slot': '{}-{}'.format(
                start_time.strftime('%H:%M'),
                end_time.strftime('%H:%M')),
                'free': schedule_public.is_time_slot_available(time_slots, start_time, end_time)})
    return time_slot


class ProjectCoachSerializers(serializers.ModelSerializer):
    user_id = serializers.IntegerField()

    class Meta:
        model = Coach
        exclude = ('created_at', 'updated_at')


class CoachResumeSerializers(serializers.ModelSerializer):
    name = serializers.CharField(source='user.cover_name')
    head_image_url = serializers.CharField(source='user.head_image_url')
    hours = serializers.SerializerMethodField()

    class Meta:
        model = Coach
        fields = ('working_years', 'language', 'brief', 'work_experience', 'domain', 'customer_evaluate', 'style',
                  'industry', 'qualification', 'explain', 'name', 'head_image_url', 'hours', 'coach_experience')

    def get_hours(self, obj):
        # TODO: 还需要加上v1中辅导的时间
        times = ProjectInterview.objects.filter(public_attr__user_id=obj.user.pk, public_attr__status__in=[3, 4, 5],
                                                public_attr__type=1, deleted=False,
                                                type=INTERVIEW_TYPE_COACHING,
                                                public_attr__end_time__lt=datetime.datetime.now()
                                                ).aggregate(times_minute=Sum('times'))
        if obj.coach_experience:
            v1_hour = int_arg(obj.coach_experience, 0)
        else:
            v1_hour = 0
        # 约谈总时长
        if times['times_minute']:
            interview_hour = int(times['times_minute'] / 60) + v1_hour
        else:
            interview_hour = v1_hour

        if interview_hour < 100:
            return '约100'
        elif interview_hour < 300:
            return '100~300'
        elif interview_hour < 500:
            return '300~500'
        elif interview_hour < 1000:
            return '500~1000'
        elif interview_hour < 2000:
            return '1000~2000'
        else:
            return '超过2000'


class TraineeCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='教练id')
    brief = serializers.CharField(help_text='教练简介')
    domain = serializers.SerializerMethodField(help_text='教练领域')
    style = serializers.CharField(help_text='教练风格')
    industry = serializers.CharField(help_text='教练过的行业')
    qualification = serializers.CharField(help_text='资质')
    coach_auth = serializers.IntegerField(help_text='教练认证')
    customer_evaluate = serializers.CharField(help_text='客户评价')
    work_experience = serializers.CharField(help_text='工作经历')
    working_years = serializers.IntegerField(help_text='工作年限')
    language = serializers.CharField(help_text='语言')
    extra_time = serializers.IntegerField(help_text='平台外工作时长')
    true_name = serializers.CharField(source='user.cover_name', help_text='教练姓名')
    head_image_url = serializers.CharField(source='user.head_image_url', help_text='教练头像')
    coach_user_id = serializers.IntegerField(source='user.pk', help_text='教练用户id')

    class Meta:
        model = TraineeCoach
        fields = ['id', 'brief', 'domain', 'style', 'industry', 'qualification', 'coach_auth', 'customer_evaluate',
                  'work_experience', 'working_years', 'language', 'extra_time', 'true_name', 'head_image_url',
                  'coach_user_id']

    def get_domain(self, obj):
        if obj.resume_id:
            resume = obj.resume
            if resume.domain:
                if len(resume.domain.split(',')) > 3:
                    return resume.domain.split(',')[:3]
                return resume.domain.split(',')
        return []


class CoachTaskSerializer(serializers.ModelSerializer):

    state = serializers.SerializerMethodField(help_text='状态 1: 教练填写，2:学员填写， 3:双方未填写， 4:双方都填写')
    data_type = serializers.SerializerMethodField(help_text='数据类型 1：辅导记录(该接口暂无)，2：教练任务，3:测评报告，4：成长目标')
    describe = serializers.SerializerMethodField(help_text='描述')
    title = serializers.SerializerMethodField(help_text='模板标题')
    image_url = serializers.SerializerMethodField(help_text='占位图')

    def get_image_url(self, obj):
        return obj.template.image_url if obj.template.image_url else NOT_SELECT_IMAGE_URL

    class Meta:
        model = CoachTask
        exclude = ('updated_at', 'public_attr')

    def get_data_type(self, obj):
        return DataType.coach_tasks

    def get_title(self, obj):
        if obj.template:
            return obj.template.title
        return

    def get_state(self, obj):
        return get_state(obj)

    def get_describe(self, obj):

        if self.get_state(obj) in [1, 4]:
            return obj.coach_submit_time.strftime('%Y-%m-%d')
        return '建议在辅导{}小时后填写'.format(int(obj.hours))


# 教练-客户详情-报告列表的教练任务序列化
class UserReportCoachTaskSerializer(serializers.ModelSerializer):
    data_type = serializers.SerializerMethodField(help_text='数据类型 1：辅导记录(该接口暂无)，2：教练任务，3:测评报告，4：成长目标')
    describe = serializers.SerializerMethodField(help_text='描述')
    image_url = serializers.SerializerMethodField(help_text='模板图片')
    title = serializers.SerializerMethodField(help_text='标题')
    details = serializers.SerializerMethodField(help_text='教练任务详情')
    project_id = serializers.SerializerMethodField(help_text='项目id')

    class Meta:
        model = CoachTask
        fields = ['data_type', 'describe', 'details', 'image_url', 'title', 'report_url', 'project_id']

    def get_project_id(self, obj):
        return obj.public_attr.project_id

    def get_data_type(self, obj):
        return DataType.coach_tasks

    def get_image_url(self, obj):
        return obj.template.image_url if obj.template.image_url else NOT_SELECT_IMAGE_URL

    def get_describe(self, obj):
        if obj.type == NewCoachTaskTypeEnum.stakeholder_research:
            return '已完成'
        if get_state(obj) in [1, 4]:
            return obj.coach_submit_time.strftime('%Y-%m-%d')
        if int(obj.hours) == 0:
            return '建议在第1次教练辅导前完成'
        return '建议在辅导{}小时后完成'.format(int(obj.hours))

    def get_title(self, obj):
        return obj.template.title if obj.template else None

    def get_details(self, obj):
        data = {
            "state": get_state(obj),
            "coach_task_type": obj.type,
            "id": obj.id,
            "write_role": obj.template.write_role,
            "created_at": obj.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        return data


def get_state(obj):
    # 双方都填写
    if obj.coach_submit_time and obj.coachee_submit_time:
        return 4
    # 双方未填写
    elif not obj.coach_submit_time and not obj.coachee_submit_time:
        return 3
    # 教练未填写
    elif not obj.coach_submit_time:
        return 2
    # 客户未填写
    else:
        return 1


class CoachProjectMemberSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='教练id')
    true_name = serializers.CharField(source='user.cover_name', help_text='姓名')
    coach_user_id = serializers.IntegerField(source='user.pk', help_text='教练用户id')
    status = serializers.SerializerMethodField(help_text='化学面谈教练状态')
    resume = serializers.SerializerMethodField(help_text='简历信息')
    appointment_time = serializers.SerializerMethodField(help_text='最近可预约时间')
    hint = serializers.SerializerMethodField(help_text='提示')

    class Meta:
        model = Coach
        fields = ['id', 'true_name', 'coach_user_id', 'status', 'resume', 'appointment_time', 'hint']

    def get_resume(self, obj):
        # 1 - 可预约2 - 已约过3 - 已约满
        project_id = self.context.get('project_id')
        state, resume = coach_public.get_project_coach_resume(obj.user_id, project_id)
        if state:
            return {"resume_id": resume.pk, "head_image_url": resume.head_image_url,
                    "domain": ','.join(resume.coach_domain) if resume.coach_domain else None}

    def get_status(self, obj):
        user_id = self.context.get('user_id')
        project_id = self.context.get('project_id')
        status = self.context.get('status')
        project_member = ProjectMember.objects.filter(user_id=user_id, project_id=project_id, deleted=False).first()
        chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
        if status != 2:  # 未进行化学面谈推荐面谈教练，配置了化学面谈
            return 1  # 可预约
        if chemical_interview_module:
            chemical_interview = chemical_interview_module.coaches.filter(deleted=False)
            # 已约过
            if chemical_interview.filter(coach=obj, interview__isnull=False).exists():
                return 2  # 已约过
            coach_offer = CoachOffer.objects.filter(project_offer__project_id=project_id, coach=obj,
                                                    status=CoachOfferStatusEnum.joined, deleted=False,
                                                    project_offer__deleted=False).first()
            if coach_offer:
                count = ChemicalInterview2Coach.objects.filter(
                    coach=obj, interview__isnull=False, deleted=False, interview__public_attr__project_id=project_id).\
                    exclude(chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
                if coach_offer.max_customer_count and count >= coach_offer.max_customer_count:
                    return 3  # 已约满
            return 1  # 可预约
        return 1  # 可预约

    def get_appointment_time(self, obj):
        return obj.appointment_time
    
    def get_hint(self, obj):
        return obj.hint


# 小程序教练端客户画像
class AppCoachCustomerDetailsPortraitSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='画像id')
    customer_experience = serializers.CharField(help_text='客户管理经验')
    city = serializers.CharField(help_text='所在城市')
    challenge = serializers.CharField(help_text='当前面临的挑战')
    coach_expect = serializers.CharField(help_text='对教练认识和期待')
    coach_extra = serializers.CharField(help_text='可供教练参考的其他信息')
    coach_id = serializers.CharField(help_text='教练id')
    user_id = serializers.CharField(help_text='用户id')
    customer_style = serializers.SerializerMethodField(help_text='个性及特点')
    gender = serializers.IntegerField(help_text='性别 1: 男 2:女')

    def get_customer_style(self, obj):
        # 如果是项目的。返回项目特点
        if obj.type == CustomerPortraitTypeEnum.group:
            return obj.group_features
        # 如果是个人的，且有个人特点，返回个人特点
        elif obj.type == CustomerPortraitTypeEnum.personal and obj.customer_style:
            return obj.customer_style
        # 如果是个人的，且没有个人特点，返回项目特点
        elif obj.project_id and obj.type == CustomerPortraitTypeEnum.personal:
            group_customer_portrait = CustomerPortrait.objects.filter(
                project_id=obj.project_id, deleted=False, type=CustomerPortraitTypeEnum.group).first()
            if group_customer_portrait:
                return group_customer_portrait.group_features
        return

    class Meta:
        model = CustomerPortrait
        fields = ('id', 'customer_style', 'customer_experience', 'city', 'challenge',
                  'user_id', 'coach_id', 'coach_expect', 'coach_extra', 'gender')


class AppCoachCustomerPortraitBaseSerializer(serializers.ModelSerializer):
    customer_style = serializers.CharField(required=False, help_text='客户个性及特点', allow_blank=True, allow_null=True)
    customer_experience = serializers.CharField(required=False, help_text='客户管理经验', allow_blank=True, allow_null=True)
    city = serializers.CharField(required=False, help_text='所在城市', allow_blank=True, allow_null=True)
    challenge = serializers.CharField(required=False, help_text='当前面临的挑战', allow_blank=True, allow_null=True)
    coach_expect = serializers.CharField(required=False, help_text='对教练认识和期待', allow_blank=True, allow_null=True)
    coach_extra = serializers.CharField(required=False, help_text='可供教练参考的其他信息', allow_blank=True, allow_null=True)
    gender = serializers.IntegerField(required=False, help_text='性别', allow_null=True)

    class Meta:
        model = CustomerPortrait
        fields = ('id', 'customer_style', 'customer_experience', 'city', 'challenge', 'coach_expect', 'coach_extra',
                  'gender', 'stakeholder_feedback_and_expectations', 'sensitive_info')

    def update(self, instance, validated_data):
        for field in self.fields:
            if field in validated_data:
                setattr(instance, field, validated_data[field])
        instance.save()
        return instance
